cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: api.dev.cxr-inc.com
    environment: dev
  datasource:
    master:
      maximum-pool-size: 150
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false

coin:
  cus:
    host: https://dev.backseat-service.com
    host-external: https://dev.backseat-service.com

gmo:
  secret: 8PBpEKucB4IktxrZngK99wpNSxvFWOcpamGNZSBrKnKjt9Q8Iq