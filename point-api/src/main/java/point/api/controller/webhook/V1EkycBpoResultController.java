package point.api.controller.webhook;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.common.model.request.BpoRequestFrom;
import point.common.service.UserEkycBpoResultService;

@Timed
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/bpo-callback")
public class V1EkycBpoResultController {

    private final UserEkycBpoResultService userEkycBpoResultService;

    @PostMapping
    public ResponseEntity<Object> call(@RequestBody BpoRequestFrom bpoRequestFrom) {
        userEkycBpoResultService.fetchBpoResultWithUpdate();
        return ResponseEntity.ok().build();
    }
}
