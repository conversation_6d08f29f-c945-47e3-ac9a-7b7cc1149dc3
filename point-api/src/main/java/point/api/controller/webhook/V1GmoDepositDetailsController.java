package point.api.controller.webhook;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.annotation.Timed;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import point.common.config.GmoConfig;
import point.common.model.request.GmoDepositDetailsForm;
import point.common.service.GmoDepositService;

@Slf4j
@Timed
@RestController
@RequestMapping("/api/v1/gmo-callback")
@RequiredArgsConstructor
public class V1GmoDepositDetailsController {

    private final GmoDepositService gmoDepositService;

    private final GmoConfig gmoConfig;

    private final ObjectMapper objectMapper;

    @PostMapping
    public ResponseEntity<Object> call(
            @RequestHeader("x-access-token") String accessToken,
            @RequestHeader("x-webhook-signature") String webhookSignature,
            @RequestHeader("User-Agent") String userAgent,
            @RequestHeader("Authorization") String authorization,
            @RequestHeader("x-eventType") String eventType,
            @RequestBody GmoDepositDetailsForm gmoDepositDetailsForm)
            throws Exception {
        try {
            // 1. Validate required parameters.
            validateRequiredParameters(
                    accessToken, webhookSignature, userAgent, authorization, eventType);

            // 2. Validate access token.
            if (!"NzYxMDAxIiwKICJhdWQiOiAiczZCaGRS".equals(accessToken)) {
                throw new UnauthorizedException("Invalid access token");
            }

            // 3. Validate webhook signature.
            validateWebhookSignature(webhookSignature, gmoDepositDetailsForm);

            // 4. Validate User-Agent.
            if (!isValidUserAgent(userAgent)) {
                throw new InvalidRequestException("Invalid User-Agent");
            }

            // 5. Validate Basic Authentication.
            validateBasicAuth(authorization);

            // 6. Validate event type.
            if (!isValidEventType(eventType)) {
                throw new InvalidRequestException("Invalid event type");
            }
            gmoDepositService.save(gmoDepositDetailsForm);
            return ResponseEntity.ok().build();
        } catch (UnauthorizedException e) {
            log.error("Authentication failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new ErrorResponse(e.getMessage()));
        } catch (InvalidRequestException e) {
            log.error("Invalid request: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ErrorResponse(e.getMessage()));
        } catch (Exception e) {
            log.error(
                    "An error occurred while processing the deposit notification.: {}",
                    e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }

    private void validateRequiredParameters(String... params) {
        for (String param : params) {
            if (StringUtils.isEmpty(param)) {
                throw new InvalidRequestException("Missing required parameter");
            }
        }
    }

    private void validateWebhookSignature(String webhookSignature, GmoDepositDetailsForm form) {
        try {
            String payload = objectMapper.writeValueAsString(form);
            String calculatedSignature = calculateHmacSha256(gmoConfig.getSecret(), payload);
            if (!calculatedSignature.equals(webhookSignature)) {
                throw new UnauthorizedException("Invalid webhook signature");
            }
        } catch (Exception e) {
            throw new InvalidRequestException("Failed to validate webhook signature");
        }
    }

    private String calculateHmacSha256(String secret, String message) throws Exception {
        Mac sha256HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey =
                new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256HMAC.init(secretKey);
        return Base64.getEncoder()
                .encodeToString(sha256HMAC.doFinal(message.getBytes(StandardCharsets.UTF_8)));
    }

    private boolean isValidUserAgent(String userAgent) {
        return userAgent.contains("GMO"); // Modify validation logic based on actual requirements.
    }

    private void validateBasicAuth(String authorization) {
        if (!authorization.startsWith("Basic ")) {
            throw new UnauthorizedException("Invalid authorization header");
        }
        // Decode and validate username and password.
        String credentials =
                new String(
                        Base64.getDecoder().decode(authorization.substring("Basic ".length())),
                        StandardCharsets.UTF_8);
        // TODO: Verify username and password
    }

    private boolean isValidEventType(String eventType) {
        // TODO: Validate event type based on business requirements.
        return true;
    }

    @Data
    @AllArgsConstructor
    private static class ErrorResponse {
        private String message;
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    private static class UnauthorizedException extends RuntimeException {
        public UnauthorizedException(String message) {
            super(message);
        }
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    private static class InvalidRequestException extends RuntimeException {
        public InvalidRequestException(String message) {
            super(message);
        }
    }
}
