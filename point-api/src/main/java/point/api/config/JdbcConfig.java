package point.api.config;

import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

@Configuration
public class JdbcConfig {

    @Bean
    @Primary
    public NamedParameterJdbcTemplate masterNamedParameterJdbcTemplate(
            @Qualifier("masterDataSource") DataSource masterDataSource) {
        return new NamedParameterJdbcTemplate(masterDataSource);
    }

    @Bean
    public NamedParameterJdbcTemplate historicalNamedParameterJdbcTemplate(
            @Qualifier("historicalDataSource") DataSource historicalDataSource) {
        return new NamedParameterJdbcTemplate(historicalDataSource);
    }
}
