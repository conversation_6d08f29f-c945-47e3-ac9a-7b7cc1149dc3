#!/bin/bash

PROJECT_NAME=cb

CHECK_AWS_ACCOUNT=0
#AWS_ACCOUNT_ALIAS=TODO
#AWS_ACCOUNT=TODO

EKS_CLUSTER_NAME=exchange

SECRET_NAMES_COMMON="base"
SECRET_NAMES_LOCAL="$SECRET_NAMES_COMMON aws-credentials"

# このスクリプトのディレクトリ
SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")

# k8s-manifests ディレクトリ
BASE_DIR=$(cd $SCRIPT_DIR/..; pwd)

export AWS_REGION=ap-northeast-1

# dont' exit when any command fails
set +e

source $SCRIPT_DIR/bash-functions.sh

## AWS 環境チェック

if [ $CHECK_AWS_ACCOUNT = 1 ]; then
  ACTUAL_AWS_ACCOUNT=$(aws sts get-caller-identity | jq -r .Account)

  if [ $AWS_ACCOUNT != "$ACTUAL_AWS_ACCOUNT" ]; then
    echo "error: $AWS_ACCOUNT_ALIAS 環境に切り替えてください。"
    exit 1
  fi
fi

## init

BASE64="base64 -w 0"
echo a | $BASE64 > /dev/null 2>&1
if [ $? != 0 ]; then
  BASE64="base64"
fi

# exit when any command fails
set -e

## process

NAMES=$SECRET_NAMES_LOCAL

DATA=""

for SECRET_NAME in $NAMES; do
  secret_id=$EKS_CLUSTER_NAME/$SECRET_NAME
  echo_label "fetch from secret: " && echo $secret_id

  value=$(aws secretsmanager get-secret-value --secret-id $secret_id)

  json=$(aws secretsmanager get-secret-value --secret-id $secret_id | jq -r .SecretString)

  for key in $(echo $json | jq -r keys[]); do
    value=$(echo $json | jq -r '.["'$key'"]')

    if [ "`echo $key | grep '-'`" ]; then
      continue
    fi

    if [ -n "$DATA" ]; then
      DATA=$DATA"\n"
    fi

    DATA=$DATA"export $key=$value"
  done
done

echo "create: local.env"
echo -e "$DATA" > local.env

echo "done."
