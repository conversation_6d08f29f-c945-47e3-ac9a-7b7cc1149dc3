package point.app.game.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.app.component.model.UserPrincipal;
import point.common.model.request.QuizUserAnswerForm;
import point.common.model.response.GlobalApiResponse;
import point.common.model.response.QuizQuestionData;
import point.common.service.QuizQuestionService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/game/quiz")
public class QuizQuestionController {

    private final QuizQuestionService quizQuestionAppService;

    @GetMapping("/questions")
    @Operation(
            summary = "quiz question info",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
                @ApiResponse(responseCode = "400", description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<QuizQuestionData>> getQuizQuestion(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal userPrincipal) {
        return quizQuestionAppService.getQuiz(userPrincipal.getUserIds());
    }

    @PostMapping("/submit")
    @Operation(
            summary = "submit question",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
                @ApiResponse(responseCode = "400", description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<String>> submitQuestion(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody QuizUserAnswerForm form)
            throws Exception {
        return quizQuestionAppService.submitQuestion(
                userPrincipal.getUserIds(),
                userPrincipal.getUserIdType(),
                form.getQuizQuestionId(),
                form.getAnswer());
    }
}
