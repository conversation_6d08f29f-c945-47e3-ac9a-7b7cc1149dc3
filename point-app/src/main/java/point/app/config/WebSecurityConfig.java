package point.app.config;

import java.util.Arrays;
import java.util.stream.Stream;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import point.app.component.*;
import point.app.component.jwt.JwtAuthenticationProcessingFilter;
import point.app.component.jwt.JwtAuthenticationProvider;
import point.app.component.jwt.JwtManager;
import point.app.component.jwt.SkipPathRequestMatcher;
import point.common.component.RedisManager;
import point.common.config.PontaConfig;
import point.common.config.SpringConfig;
import point.common.service.ChoicePowerSyncService;
import point.common.service.OneTimePasswordService;
import point.common.service.PontaUserCallbackService;
import point.common.service.UserLoginInfoService;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    private final AccessDeniedHandler accessDeniedHandler;

    private final AuthenticationEntryPoint authenticationEntryPoint;

    private final AuthenticationFailureHandler authenticationFailureHandler;

    private final AuthenticationProvider authenticationProvider;

    private final AuthenticationSuccessHandler authenticationSuccessHandler;

    private final PointAppConfig exchangeAppConfig;

    private final LogoutSuccessHandler logoutSuccessHandler;

    private final SpringConfig springConfig;

    // -- required by PontaAuthenticationSuccessHandlerImpl
    private final IpAddressResolver ipAddressResolver;
    private final UserLoginInfoService userLoginInfoService;
    private final JwtManager jwtManager;
    private final RedisManager redisManager;
    private final OneTimePasswordService oneTimePasswordService;
    private final ChoicePowerSyncService choicePowerSyncService;

    @Value("${jwt.cache-token-ttl}")
    private Integer jwtCacheTtl;

    @Value("${ponta.otp-ttl}")
    private Integer otpTtl;

    // -- required by PontaAuthenticationSuccessHandlerImpl

    // -- required by PontaAuthenticationProviderImpl
    private final PontaUserCallbackService pontaUserCallbackService;
    // -- required by PontaAuthenticationProviderImpl

    private final PontaConfig pontaConfig;

    @Value("${point-app.security.enable-invest-login-whitelist}")
    private boolean enableInvestLoginWhitelistFlag;

    private final String tokenBasedAuthEntryPoint = "/app/**";
    private static final String[] BASE_WHITE_LIST = {
        "/",
        "/healthcheck",
        "/actuator/**",
        "/app/healthcheck",
        "/app/v1/candlestick",
        "/app/v1/contact",
        "/app/v1/country",
        "/app/v1/currency",
        "/app/v1/currency-pair",
        "/app/v1/news",
        "/app/v1/orderbook",
        "/app/v1/symbol",
        "/app/v1/ticker",
        "/app/v1/trades",
        "/app/v1/user/logout",
        "/app/v1/user/reset-password",
        "/app/v1/user/reset-password/otpauth",
        "/app/v1/user/register",
        "/app/v1/user/register/otpauth",
        "/app/v1/refresh-token",
        "/app/v1/user/password",
        "/app/v1/gmo/oauth",
        "/app/v1/pos/price",
        "/app/v1/pos/currency-pair",
        "/app/v1/pos/candlestick",
        "/app/v1/pos/price/all",
        "/app/v1/pos/order/control",
        "/app/v1/currency-pair/display",
        "/websocket",
        "/app/v1/ponta-callback",
        "/app/v1/operate/mock/success",
        "/app/v1/operate/mock/failure",
        "/app/v1/operate/mock/login",
        "/app/v1/operate/user/ponta-login",
        "/app/v1/operate/user/token",
        "/app/swagger-ui.html",
        "/app/swagger-ui/**",
        "/app/v3/api-docs",
        "/app/v3/api-docs/**",
        "/webjars/**",
        "/app/v1/operate/price",
        "/app/v1/operate/candlestick",
        "/app/v1/game/home/<USER>",
    };

    private static final String[] LOGIN_WHITE_LIST = {
        "/app/v1/user/login", "/app/v1/user/login/otpauth"
    };

    @PostConstruct
    public void init() {
        if (enableInvestLoginWhitelistFlag) {
            whiteListEntryPoint =
                    Stream.concat(Arrays.stream(BASE_WHITE_LIST), Arrays.stream(LOGIN_WHITE_LIST))
                            .toArray(String[]::new);
        } else {
            whiteListEntryPoint = BASE_WHITE_LIST;
        }
    }

    public static String[] whiteListEntryPoint;

    private CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedMethod(CorsConfiguration.ALL);
        corsConfiguration.addAllowedHeader(CorsConfiguration.ALL);
        corsConfiguration.setAllowCredentials(true);

        if (springConfig.isLocal() || springConfig.isDev()) {
            corsConfiguration.addAllowedOriginPattern(CorsConfiguration.ALL);
        } else {
            corsConfiguration.addAllowedOrigin(exchangeAppConfig.getAllowedOrigin());
        }

        UrlBasedCorsConfigurationSource corsConfigurationSource =
                new UrlBasedCorsConfigurationSource();
        corsConfigurationSource.registerCorsConfiguration("/**", corsConfiguration);
        return corsConfigurationSource;
    }

    /*  @Override
    public void configure(WebSecurity web) {
      web.ignoring()
          .antMatchers(
              "/webjars/**",
              "/css/**",
              "/js/**",
              "/favicon.ico",
              "/swagger-ui.html",
              "/v2/api-docs", // swagger api json
              "/swagger-resources/configuration/ui",
              "/swagger-resources",
              "/swagger-resources/configuration/security",
              "/swagger-resources/**",
              "/webjars/**");
    }*/

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.authenticationProvider(authenticationProvider)
                .authenticationProvider(this.jwtAuthenticationProvider());
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        AuthenticationProcessingFilter authenticationProcessingFilter =
                new AuthenticationProcessingFilter(authenticationManager());
        authenticationProcessingFilter.setAuthenticationSuccessHandler(
                authenticationSuccessHandler);
        authenticationProcessingFilter.setAuthenticationFailureHandler(
                authenticationFailureHandler);

        JwtAuthenticationProcessingFilter jwtAuthenticationProcessingFilter =
                new JwtAuthenticationProcessingFilter(
                        authenticationManager(),
                        new SkipPathRequestMatcher(
                                Arrays.asList(whiteListEntryPoint), tokenBasedAuthEntryPoint));
        jwtAuthenticationProcessingFilter.setAuthenticationFailureHandler(
                authenticationFailureHandler);

        // ponta user login filter
        PontaAuthenticationProcessingFilter pontaAuthenticationProcessingFilter =
                new PontaAuthenticationProcessingFilter(pontaAuthenticationManager());
        pontaAuthenticationProcessingFilter.setAuthenticationFailureHandler(
                new PontaAuthenticationFailureHandlerImpl(pontaConfig));
        pontaAuthenticationProcessingFilter.setAuthenticationSuccessHandler(
                new PontaAuthenticationSuccessHandlerImpl(
                        pontaConfig,
                        ipAddressResolver,
                        userLoginInfoService,
                        jwtManager,
                        redisManager,
                        jwtCacheTtl,
                        otpTtl,
                        oneTimePasswordService,
                        choicePowerSyncService));

        http.authorizeRequests()
                .mvcMatchers(whiteListEntryPoint)
                .permitAll()
                .anyRequest()
                .authenticated()
                .and()
                .exceptionHandling()
                .authenticationEntryPoint(authenticationEntryPoint)
                .accessDeniedHandler(accessDeniedHandler)
                .and()
                .logout()
                .logoutUrl("/app/v1/user/logout")
                .logoutSuccessHandler(logoutSuccessHandler)
                .permitAll()
                .and()
                .csrf()
                .disable()
                .cors()
                .configurationSource(corsConfigurationSource())
                .and()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .addFilterAt(
                        authenticationProcessingFilter, UsernamePasswordAuthenticationFilter.class)
                .addFilterAt(
                        pontaAuthenticationProcessingFilter,
                        UsernamePasswordAuthenticationFilter.class)
                .addFilterAt(
                        jwtAuthenticationProcessingFilter,
                        UsernamePasswordAuthenticationFilter.class);
    }

    @Bean
    public JwtAuthenticationProvider jwtAuthenticationProvider() {
        return new JwtAuthenticationProvider();
    }

    public AuthenticationManager pontaAuthenticationManager() {
        return new ProviderManager(new PontaAuthenticationProviderImpl(pontaUserCallbackService));
    }
}
