package point.app.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import point.common.component.CustomRedisTemplate;
import point.common.service.BcCurrencyConfigService;
import point.common.service.CurrencyPairConfigService;
import point.common.service.SymbolService;
import point.common.websocket.RedisPublisher;
import point.pos.model.PosBestPriceData;
import point.pos.service.*;

@Configuration
public class PosConfig {
    @Bean
    public PosBestPriceService posBestPriceService(
            SymbolService symbolService,
            CurrencyPairConfigService currencyPairConfigService,
            PosAmberBestPriceService amberBestPriceService,
            CustomRedisTemplate<PosBestPriceData> bestPriceRedisTemplate,
            PosBestPriceArchiveService archiveService,
            PosMarketMakerConfigService posMarketMakerConfigService,
            PosTradeService posTradeService,
            PosCandlestickService posCandlestickService,
            RedisPublisher redisPublisher,
            BcCurrencyConfigService bcCurrencyConfigService) {
        return new PosBestPriceService(
                symbolService,
                currencyPairConfigService,
                amberBestPriceService,
                bestPriceRedisTemplate,
                archiveService,
                posMarketMakerConfigService,
                posTradeService,
                posCandlestickService,
                redisPublisher,
                bcCurrencyConfigService);
    }

    @Bean
    public PosAmberBestPriceService posAmberBestPriceService() {
        return new PosAmberBestPriceService();
    }
}
