package point.app.config.websocket;

import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.messaging.simp.SimpMessageSendingOperations;
import org.springframework.stereotype.Service;
import point.common.component.CustomRedisTemplate;
import point.common.constant.TradeType;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.Symbol;
import point.common.event.publisher.EventPublisher;
import point.common.model.response.*;
import point.common.model.response.websocket.*;
import point.common.service.CurrencyPairConfigService;
import point.common.service.OrderbookService;
import point.common.service.SymbolService;
import point.common.util.DigestUtils;
import point.pos.model.PosBestPriceData;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(
        name = "exchange-websocket.redis-pubsub-subscriber.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class RedisSubscriber implements MessageListener {
    private final SimpMessageSendingOperations messagingTemplate;
    private final OrderbookService orderbookService;
    private final CurrencyPairConfigService currencyPairConfigService;
    private final SymbolService symbolService;
    private final CustomRedisTemplate<String> redisTemplate;
    private final DestinationConverter destinationConverter;
    private final StompBrokerProperties stompBrokerProperties;
    private final EventPublisher eventPublisher;

    private final ChannelTopic assetTopic;
    private final ChannelTopic currencyPairTopic;
    private final ChannelTopic posCandlestickTopic;
    private final ChannelTopic posPriceTopic;
    private final ChannelTopic posTradeUpdateTopic;
    private final ChannelTopic choiceActivityTopic;

    public void onMessage(final Message message, final byte[] pattern) {
        log.debug("Message received from Redis PubSub: " + new String(message.getBody()));
        String topic = new String(pattern);

        if (stompBrokerProperties.isEnabled()) {
            // When we use external broker, we should make sure there is only one subscriber
            // handling the message
            String messageId =
                    "ws:message-handler"
                            + topic
                            + ":"
                            + DigestUtils.md5(new String(message.getBody()));
            log.debug("messageId {}", messageId);
            if (!redisTemplate.setIfAbsent(
                    messageId,
                    "true",
                    stompBrokerProperties.getSingleConsumerLockExpiredInSeconds())) {
                log.info("message {} is handled by other server!", messageId);
                return;
            }
        }

        if (assetTopic.getTopic().equals(topic)) {
            AssetDataWrapper data = (AssetDataWrapper) deserializeMessage(message.getBody());
            BigDecimal jpyConversion;
            try {
                jpyConversion = orderbookService.getBestBidOfQuoteJpy(data.getCurrency());
            } catch (Exception e) {
                throw new IllegalStateException("Invalid currency", e);
            }
            String destination = destinationConverter.determineRealDestination("/queue/asset");
            this.messagingTemplate.convertAndSendToUser(
                    data.getUserId().toString(), destination, data.unwrap(jpyConversion));
        } else if (currencyPairTopic.getTopic().equals(topic)) {
            CurrencyPairConfigDataWrapper data =
                    (CurrencyPairConfigDataWrapper) deserializeMessage(message.getBody());
            String destination =
                    destinationConverter.determineRealDestination("/topic/currency-pair");
            log.debug("Send to destination: " + destination);
            this.messagingTemplate.convertAndSend(destination, data.unwrap());

            if (TradeType.INVEST.equals(data.getConfig().getTradeType())) {
                destination =
                        destinationConverter.determineRealDestination("/topic/pos/currency-pair");
                this.messagingTemplate.convertAndSend(destination, data.unwrapPos());
            }
        } else if (posCandlestickTopic.getTopic().equals(topic)) {
            PosCandlestickDataWrapper dataWrapper =
                    (PosCandlestickDataWrapper) deserializeMessage(message.getBody());
            Long symbolId = dataWrapper.getSymbolId();
            String destination =
                    destinationConverter.determineRealDestination(
                            "/topic/pos/candlestick.symbol."
                                    + symbolId
                                    + "."
                                    + dataWrapper.getCandlestickType());
            log.debug("Send to destination: " + destination);
            PosCandlestickData data =
                    isCurrencyPairEnabled(symbolId)
                            ? dataWrapper.unwrap()
                            : dataWrapper.unwrapToEmpty();
            this.messagingTemplate.convertAndSend(destination, data);
        } else if (posPriceTopic.getTopic().equals(topic)) {
            PosBestPriceData data = (PosBestPriceData) deserializeMessage(message.getBody());
            String destination =
                    destinationConverter.determineRealDestination(
                            "/topic/pos/price.symbol." + data.getSymbolId());
            log.debug("Send to destination: " + destination);
            this.messagingTemplate.convertAndSend(destination, data);
        } else if (posTradeUpdateTopic.getTopic().equals(topic)) {
            PosTradeDataWrapper data = (PosTradeDataWrapper) deserializeMessage(message.getBody());
            String destination =
                    destinationConverter.determineRealDestination(
                            "/queue/pos/trade.symbol." + data.getSymbolId());
            log.debug("Send to destination: " + destination);
            this.messagingTemplate.convertAndSendToUser(
                    data.getUserId().toString(), destination, data.unwrap());
        } else if (choiceActivityTopic.getTopic().equals(topic)) {
            ChoiceActivityDataWrapper data =
                    (ChoiceActivityDataWrapper) deserializeMessage(message.getBody());
            eventPublisher.publishChoiceActivityTotalChangedEvent(data);
        } else {
            // Unexpected message
            log.warn("No handler was implemented for this topic: " + topic);
        }
    }

    private Object deserializeMessage(byte[] body) {
        GenericJackson2JsonRedisSerializer serializer = new GenericJackson2JsonRedisSerializer();
        return serializer.deserialize(body);
    }

    private boolean isCurrencyPairEnabled(Long symbolId) {
        Symbol symbol = symbolService.findOne(symbolId);
        if (symbol == null) {
            return false;
        }
        CurrencyPairConfig config =
                currencyPairConfigService.findOne(symbol.getTradeType(), symbol.getCurrencyPair());
        if (config == null) {
            return false;
        }
        return config.isEnabled();
    }
}
