package point.app.config.websocket;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class DestinationConverter {

    private final StompBrokerProperties stompBrokerProperties;

    // determine the real destination
    public String determineRealDestination(String destination) {
        log.debug("original destination {}", destination);
        if (destination == null) {
            return null;
        }
        if (!stompBrokerProperties.isEnabled()) {
            return destination;
        }
        // /user/queue/ -> /user/exchange/amq.direct/
        // /user/queue/pos/* -> /user/exchange/amq.direct/pos.*
        // /topic/pos/* -> /topic/pos.*
        return destination
                .replaceAll("/queue/", "/exchange/amq.direct/")
                .replaceAll("/pos/", "/pos.");
    }
}
