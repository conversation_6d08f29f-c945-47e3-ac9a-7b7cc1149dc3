package point.app.component;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import point.common.config.PontaConfig;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.exception.PontaAuthenticationException;
import point.common.exception.PontaUserCancelConsentAuthenticationException;
import point.common.ponta.PontaRedirectUtil;

@Slf4j
@RequiredArgsConstructor
public class PontaAuthenticationFailureHandlerImpl implements AuthenticationFailureHandler {

    private final PontaConfig pontaConfig;

    @Override
    public void onAuthenticationFailure(
            HttpServletRequest request,
            HttpServletResponse response,
            AuthenticationException exception)
            throws IOException, ServletException {
        log.info("Operate user login failed, error message: {}", exception.getMessage(), exception);

        String client = request.getParameter(CommonConstants.CLIENT);

        ErrorCode errorCode = ErrorCode.COMMON_ERROR_SYSTEM_ERROR;
        if (exception instanceof PontaAuthenticationException ex) {
            errorCode = ex.getErrorCode();
        }

        if (exception instanceof PontaUserCancelConsentAuthenticationException) {
            String partnerNumber = request.getParameter(CommonConstants.PONTA_PARTNER_NUMBER);
            response.sendRedirect(
                    PontaRedirectUtil.getFailureUri(pontaConfig, client, partnerNumber));
            return;
        }

        response.sendRedirect(
                PontaRedirectUtil.getFailureUriWithError(pontaConfig, client, errorCode));
    }
}
