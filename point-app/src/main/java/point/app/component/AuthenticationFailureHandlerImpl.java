package point.app.component;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;
import point.common.constant.ErrorCode;
import point.common.entity.User;
import point.common.model.response.ErrorData;
import point.common.service.LoginAttemptService;
import point.common.service.UserService;

@RequiredArgsConstructor
@Component
public class AuthenticationFailureHandlerImpl implements AuthenticationFailureHandler {

    private final UserService userService;

    private final LoginAttemptService loginAttemptService;

    @Override
    public void onAuthenticationFailure(
            HttpServletRequest request,
            HttpServletResponse response,
            AuthenticationException exception)
            throws IOException, ServletException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.getWriter()
                .write(
                        new ObjectMapper()
                                .writeValueAsString(
                                        new ErrorData(
                                                ErrorCode.valueOfCode(
                                                        Integer.valueOf(exception.getMessage())))));

        if (exception.getClass() == BadCredentialsException.class) {
            User user = userService.loadUserByUsername(request.getParameter("email"));
            if (user != null) {
                if (loginAttemptService.countUp(user.getId())) {
                    user.setAccountNonLocked(false);
                    userService.saveWithAuthenticationPrincipal(user);
                }
            }
        }
    }
}
