package point.app.component.jwt;

import com.auth0.jwt.interfaces.Claim;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.util.StringUtils;
import point.app.component.model.UserPrincipal;
import point.app.component.model.UserWrapper;
import point.common.component.RedisManager;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.constant.UserIdType;
import point.common.entity.PointUser;
import point.common.entity.User;
import point.common.service.PointUserService;
import point.common.service.UserService;

@Slf4j
@RequiredArgsConstructor
public class JwtAuthenticationProvider implements AuthenticationProvider {

    @Autowired private JwtManager jwtManager;
    @Autowired private UserService userService;
    @Autowired private RedisManager redisManager;
    @Autowired private PointUserService pointUserService;

    @Override
    public Authentication authenticate(Authentication authentication)
            throws AuthenticationException {
        String token = (String) authentication.getCredentials();

        Map<String, Claim> claims = JwtTokenUtil.getClaim(jwtManager, token);
        if (!JwtTokenUtil.isAccessToken(jwtManager, claims)) {
            log.info("jwt token scope invalid");
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }

        Claim claim = claims.get(CommonConstants.USER_ID);
        if (Objects.isNull(claim) || Objects.isNull(claim.asLong())) {
            log.info("jwt token cannot have a valid user_id");
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }
        Long userId = claim.asLong();
        String redisToken = JwtTokenUtil.getJwtFromRedis(redisManager, userId);
        if (!StringUtils.hasText(redisToken)) {
            log.info("The JWT token does not exist in redis : {}", userId);
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }
        if (!token.equals(redisToken)) {
            log.info("The JWT tokens are inconsistent in redis : {}", userId);
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }

        Claim claimIdType = claims.get(CommonConstants.ID_TYPE);
        if (claimIdType == null || claimIdType.asString() == null) {
            log.info("jwt token cannot have a valid id_type");
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }
        String idType = claimIdType.asString();
        if (idType.equals(UserIdType.Invest.toString())) {
            User user = userService.findOne(userId);
            if (user == null) {
                log.info("Cannot get invest_user by id: {}", claim.asLong());
                throw new AuthenticationServiceException(
                        Integer.toString(ErrorCode.REQUEST_ERROR_USER_IS_NULL.getCode()));
            }

            PointUser pointUser = pointUserService.findByUserId(userId);
            UserPrincipal userPrincipal =
                    UserPrincipal.from(
                            user.getId(),
                            UserIdType.Invest,
                            user.getEmail(),
                            UserWrapper.builder().user(user).pointUser(pointUser).build());
            return new JwtAuthenticationToken(userPrincipal, Collections.emptyList());
        } else if (idType.equals(UserIdType.Operate.toString())) {

            PointUser pointUser = pointUserService.findOne(userId);
            if (pointUser == null) {
                log.info("Cannot get point_user by id: {}", claim.asLong());
                throw new AuthenticationServiceException(
                        Integer.toString(ErrorCode.REQUEST_ERROR_USER_IS_NULL.getCode()));
            }
            User investUser = null;
            if (Objects.nonNull(pointUser.getUserId())) {
                investUser = new User();
                investUser.setId(pointUser.getUserId());
            }
            UserPrincipal userPrincipal =
                    UserPrincipal.from(
                            pointUser.getId(),
                            UserIdType.Operate,
                            null,
                            UserWrapper.builder().pointUser(pointUser).user(investUser).build());
            return new JwtAuthenticationToken(userPrincipal, Collections.emptyList());
        } else {
            log.info("The JWT token does not have a valid id_type : {}", userId);
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return JwtAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
