package point.app.component;

import javax.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class IpAddressResolver {

    /**
     * getRemoteAddr クライアントのIPアドレスを返す(ELB等を経由していたらxForwardedForを返す)
     *
     * @param HttpServletRequest request
     * @return クライアントのIPアドレス
     */
    public String getRemoteAddr(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null) {
            return xForwardedFor;
        }
        return request.getRemoteAddr();
    }
}
