package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.*;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thymeleaf.util.StringUtils;
import point.app.component.MfaManager;
import point.app.component.model.UserPrincipal;
import point.app.config.GlobalAuthenticationConfig;
import point.app.invest.model.request.ResetPasswordPutForm;
import point.app.invest.model.request.UserAgreementForm;
import point.app.invest.model.request.UserAntiPhishingCodeOtpauthPutForm;
import point.app.invest.model.request.UserAntiPhishingCodePutForm;
import point.app.invest.model.request.UserEmailPutForm;
import point.app.invest.model.request.UserLoginOtpauthPostForm;
import point.app.invest.model.request.UserPasswordForm;
import point.app.invest.model.request.UserPasswordOtpauthPutForm;
import point.app.invest.model.request.UserPasswordPutForm;
import point.app.invest.model.request.UserRegisterPostForm;
import point.common.component.CustomTransactionManager;
import point.common.component.RedisManager;
import point.common.component.SesManager;
import point.common.config.ApplicationConfig;
import point.common.config.SpringConfig;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.exception.GmoRequestException;
import point.common.model.request.EmailForm;
import point.common.model.response.MfaTypeData;
import point.common.model.response.UserConditionData;
import point.common.model.response.UserLevelData;
import point.common.model.response.UserRegisterResponse;
import point.common.service.*;
import point.common.util.DateUnit;
import point.common.util.StringUtil;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/user")
@Timed
@Slf4j
public class V1UserRestController {

    private final AssetService assetService;

    private final GlobalAuthenticationConfig globalAuthenticationConfig;

    private final LoginAttemptService loginAttemptService;

    private final MailNoreplyService mailNoreplyService;

    private final MfaManager mfaManager;

    private final SesManager sesManager;

    private final SpringConfig springConfig;

    private final UserAgreementService userAgreementService;

    private final UserAuthorityService userAuthorityService;

    private final UserInfoService userInfoService;

    private final UserKycService userKycService;

    private final UserLoginInfoService userLoginInfoService;

    private final UserMfaService userMfaService;

    private final UserService userService;

    private final UserMailNoticesOffService userMailNoticesOffService;

    private final UserEkycService userEkycService;

    private final FiatDepositService fiatDepositService;

    @Value("${mail.message.account-created.base-url:#{null}}")
    private String accountCreateUrl;

    @Value("${customer.forgot-password.token.forgot-effective-time:60000}")
    private String forgotEffectiveTime;

    @Value("${customer.login-password.token.effective-time:********}")
    private String passwordEffectiveTime;

    private final RedisManager redisManager;

    private final CustomTransactionManager customTransactionManager;

    private final OnetimeBankAccountService onetimeBankAccountService;

    private final AffiliateInfoService affiliateInfoService;

    private final UserKycSubService userKycSubService;
    private final UserRegistrationService userRegistrationService;

    @GetMapping
    public ResponseEntity<Object> get(@AuthenticationPrincipal UserPrincipal user) {
        return ResponseEntity.ok(user);
    }

    private void agreeDocuments(Long userId, EntityManager entityManager) throws Exception {
        userAgreementService.agreeDocuments(
                userId,
                List.of(
                        UserAgreementType.DISCLOSURE_STATEMENT,
                        UserAgreementType.TERMS_OF_SERVICE,
                        UserAgreementType.PRIVACY_POLICY),
                entityManager);
    }

    private void addAsset(Long userId, EntityManager entityManager) throws Exception {
        if (
        /*springConfig.isStg() || */ springConfig.isPrd()) {
            return;
        }

        {
            // 資産を1000万円追加する
            Asset asset = new Asset();
            asset.setUserId(userId);
            asset.setCurrency(Currency.JPY);
            asset.setOnhandAmount(BigDecimal.valueOf(********));
            asset.setLockedAmount(BigDecimal.ZERO);
            assetService.save(asset, entityManager);

            // assetSummaryがズレないように入金データも1000万円追加する
            FiatDeposit fiatDeposit = new FiatDeposit();
            fiatDeposit.setUserId(userId);
            fiatDeposit.setBankAccountId(null);
            fiatDeposit.setOnetimeBankAccountId(1L); // 入ってさえいれば問題ないと思われるためデータのある1とする
            fiatDeposit.setAmount(BigDecimal.valueOf(********));

            fiatDeposit.setFee(BigDecimal.ZERO);
            fiatDeposit.setFiatDepositStatus(FiatDepositStatus.DONE);
            fiatDeposit.setFiatDepositSubStatus(null);
            fiatDeposit.setComment(null);
            fiatDepositService.save(fiatDeposit);
        }
    }

    @PostMapping("/password")
    public ResponseEntity<Object> setPwd(@Valid @RequestBody UserPasswordForm form)
            throws Exception {
        User user = userService.findOne(form.getCusId());
        String token = redisManager.get("register:code:" + user.getEmail());
        if (StringUtils.isEmpty(token)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_EXPIRE_TOKEN);
        }
        if (!form.getToken().equals(token)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_TOKEN);
        }

        customTransactionManager.execute(
                entityManager -> {
                    final var newUserKyc = new UserKyc(user.getId());
                    newUserKyc.setKycStatus(KycStatus.NONE);
                    newUserKyc.setOperator(CommonConstants.APP);
                    newUserKyc.setUserInfoId(user.getUserInfoId());
                    userKycService.save(newUserKyc, entityManager);

                    user.setPassword(
                            globalAuthenticationConfig.getBcryptHashpw(form.getPassword()));
                    user.setUserKycId(newUserKyc.getId());
                    user.setKycStatus(newUserKyc.getKycStatus());
                    userService.save(user);

                    redisManager.delete("register:code:" + user.getEmail());

                    MailNoreply mailNoreply =
                            mailNoreplyService.findOne(MailNoreplyType.PASSWORD_COMPLETE);
                    try {
                        sesManager.send(
                                mailNoreply.getFromAddress(),
                                user.getEmail(),
                                mailNoreply.getTitle(),
                                mailNoreply.getContents());
                    } catch (Exception e) {
                        log.error(
                                "send set password mail fail , please send mail manually,email:{}",
                                user.getEmail());
                    }
                });
        return ResponseEntity.ok().build();
    }

    @PostMapping("/gmo/issue")
    public ResponseEntity<Void> issueAccount(@AuthenticationPrincipal UserPrincipal user)
            throws GmoRequestException {
        onetimeBankAccountService.issueVirtualAccount(user.getId());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/register")
    public ResponseEntity<UserRegisterResponse> postRegister(
            @Valid @RequestBody UserRegisterPostForm form,
            HttpServletRequest request,
            HttpServletResponse httpResponse)
            throws Exception {
        UserRegisterResponse response = new UserRegisterResponse();
        String email = form.getEmail();
        validateEmail(email);
        InvestUser investUser = userRegistrationService.findByEmail(email);
        if (investUser != null && investUser.getPassword() != null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_USER_EXIST);
        }
        String key = "register:code:" + form.getEmail();
        String result = redisManager.get(key);
        if (!StringUtils.isEmpty(result)) {
            throw new CustomException(ErrorCode.REGISTER_TOKEN_NOT_EXPIRE);
        }
        customTransactionManager.execute(
                entityManager -> {
                    InvestUser user = investUser;
                    String affiliateIdentify = null;
                    String uuid = "cb_" + UUID.randomUUID();

                    // first register
                    if (Objects.isNull(user)) {
                        // -- create user
                        user = new InvestUser(form.getEmail());
                        // set user properties from cookies
                        if ((StringUtils.isEmpty(form.getAuthority())
                                        || Authority.PERSONAL.equals(
                                                Authority.valueOf(form.getAuthority())))
                                && request.getHeader("cookie") != null) {
                            String[] strs = request.getHeader("cookie").split(";");
                            Map<String, String> map = new HashMap<String, String>();
                            for (String string : strs) {
                                map.put(string.split("=")[0].trim(), string.split("=")[1].trim());
                            }
                            for (Map.Entry<String, String> cookie : map.entrySet()) {
                                AffiliateInfo affiliateInfo =
                                        affiliateInfoService.findOneByIdentify(cookie.getKey());
                                if (ObjectUtils.isNotEmpty(affiliateInfo)) {
                                    affiliateIdentify = cookie.getKey();
                                    user.setSessionId(
                                            URLDecoder.decode(
                                                    cookie.getValue(), StandardCharsets.UTF_8));
                                    user.setAffiliateInfoId(affiliateInfo.getId());
                                    user.setUuid(uuid);
                                    break;
                                }
                            }
                        }
                        // set user kyc status
                        user.setKycStatus(KycStatus.WAITING_SET_PWD);
                        // save user
                        userRegistrationService.save(user, entityManager);

                        // -- user authority
                        UserAuthority userAuthority = new UserAuthority();
                        userAuthority.setUserId(user.getId());
                        if (StringUtils.isEmpty(form.getAuthority())) {
                            userAuthority.setAuthority(Authority.PERSONAL);
                        } else {
                            userAuthority.setAuthority(Authority.valueOf(form.getAuthority()));
                        }
                        userAuthorityService.save(userAuthority, entityManager);

                        // -- user agreement
                        agreeDocuments(user.getId(), entityManager);

                        // -- user kyc
                        UserKyc userKyc = new UserKyc(user.getId());
                        userKyc.setKycMailStatus(KycMailStatus.MAIL_SENT);
                        userKyc.setMailSendAt(new Date());
                        userKyc.setOperator(CommonConstants.APP);
                        userKyc.setUserInfoId(user.getUserInfoId());
                        userKycService.save(userKyc, entityManager);

                        // save user with kyc relation
                        user.setUserKycId(userKyc.getId());
                        // save user again
                        userRegistrationService.save(user, entityManager);
                        // init asset
                        addAsset(user.getId(), entityManager);

                        // -- create user mail notice
                        userMailNoticesOffService.create(user.getId(), entityManager);
                    }
                    MailNoreply mailNoreply =
                            mailNoreplyService.findOne(MailNoreplyType.ACCOUNT_CREATED);
                    String token = Security.createToken();
                    Long exp = System.currentTimeMillis() + Long.parseLong(passwordEffectiveTime);
                    String url =
                            MessageFormat.format(
                                    accountCreateUrl,
                                    user.getId().toString(),
                                    token,
                                    exp.toString());
                    String content =
                            formatContent(
                                    mailNoreply.getContents(),
                                    url,
                                    DateUnit.formatJapanDate(
                                            calculateExpire(TokenType.REGISTER.name())));
                    response.setId(user.getId());
                    response.setEmail(user.getEmail());
                    if (springConfig.isNotPrd()) {
                        response.setUrl(url);
                    }
                    response.setUuid(uuid);
                    response.setAffiliateFrom(affiliateIdentify);
                    redisManager.set(key, token, Integer.parseInt(passwordEffectiveTime) / 60000);
                    boolean send =
                            sesManager.send(
                                    mailNoreply.getFromAddress(),
                                    user.getEmail(),
                                    mailNoreply.getTitle(),
                                    content);
                    if (!send) {
                        log.info("register user invoke the email service Exception");
                        redisManager.delete(key);
                        throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
                    }
                });
        return ResponseEntity.ok(response);
    }

    public String formatContent(String content, String... arg) {
        MessageFormat format = new MessageFormat(content);
        return format.format(arg);
    }

    public Long calculateExpire(String tokenType) {
        long current = System.currentTimeMillis();
        String effectiveTime = passwordEffectiveTime;
        if (!TokenType.REGISTER.name().equals(tokenType)) {
            effectiveTime = forgotEffectiveTime;
        }
        return current + Long.parseLong(effectiveTime);
    }

    @PostMapping("/login/otpauth")
    public ResponseEntity<MfaTypeData> postRegisterOtpauth(
            @Valid @RequestBody UserLoginOtpauthPostForm form) throws Exception {
        User user = userService.findByEmail(form.getEmail());

        if (user == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_USER_IS_NULL);
        }

        if (!user.isAccountNonLocked() && !loginAttemptService.isLockExpired(user.getId())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_UNAUTHORIZED_ACCOUNT_LOCKED);
        } else if (!user.isAllowedToLogin()) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_ACCOUNT_STOPPED);
        }

        if (!globalAuthenticationConfig.matchesBCryptPassword(
                form.getPassword(), user.getPassword())) {
            if (loginAttemptService.countUp(user.getId())) {
                String key = CommonConstants.REDIS_USER_TOCKN_PREFIX + user.getId();
                redisManager.delete(key);
                user.setAccountNonLocked(false);
                userService.saveWithAuthenticationPrincipal(user);
            }

            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER_PASSWORD);
        }

        return mfaManager.responseMfaTypeData(user.getId(), user.getEmail());
    }

    private void validateEmail(String email) throws Exception {
        switch (springConfig.getEnvironment()) {
            case prd -> {
                if (!StringUtil.validateEmailExceptAlias(email)) {
                    throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_EMAIL);
                }
            }
            default -> {
                if (!StringUtil.validateEmail(email)) {
                    throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_EMAIL);
                }
            }
        }
    }

    @PutMapping("/email/verify")
    public ResponseEntity<MfaTypeData> putEmailVerify(
            @AuthenticationPrincipal UserPrincipal user, @Valid @RequestBody EmailForm form)
            throws Exception {
        validateEmail(form.getEmail());

        User userByEmail = userService.findByEmail(form.getEmail());
        if (userByEmail != null
                && userByEmail.getPassword() != null
                && !userByEmail.getUserStatus().equals(UserStatus.LEFT)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_USER_EXIST);
        }

        return mfaManager.responseMfaTypeData(form.getEmail(), MfaType.EMAIL);
    }

    @PutMapping("/email/otpauth")
    public ResponseEntity<MfaTypeData> putEmailOtpauth(
            @AuthenticationPrincipal UserPrincipal user, @Valid @RequestBody UserEmailPutForm form)
            throws Exception {
        validateEmail(form.getEmail());

        User userByEmail = userService.findByEmail(form.getEmail());
        if (userByEmail != null
                && userByEmail.getPassword() != null
                && !userByEmail.getUserStatus().equals(UserStatus.LEFT)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_USER_EXIST);
        }

        mfaManager.authenticateBySmsOrEmail(form.getEmail(), form.getMfaCode());
        return mfaManager.responseMfaTypeData(user.getId(), user.getEmail());
    }

    @PutMapping("/email")
    public ResponseEntity<MfaTypeData> putEmail(
            @AuthenticationPrincipal UserPrincipal user, @Valid @RequestBody UserEmailPutForm form)
            throws Exception {
        validateEmail(form.getEmail());
        mfaManager.authenticate(user.getId(), user.getEmail(), form.getMfaCode());
        User userForUpdate = userService.findOne(user.getId());

        UserKyc userKyc = new UserKyc(user.getId());
        List<UserKyc> userKycList =
                userKycService
                        .findByUserId(user.getId())
                        .orElseThrow(
                                () -> new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER));
        UserKyc latestUserKyc =
                userKycList.stream()
                        .sorted((u1, u2) -> u2.getId().compareTo(u1.getId()))
                        .findFirst()
                        .orElse(null);
        userKyc.setKycStatus(latestUserKyc.getKycStatus());
        userKyc.setOperator(CommonConstants.APP);
        userKyc.setEmail(user.getEmail());
        userKyc.setUserInfoId(userForUpdate.getUserInfoId());
        userKyc.setChangeType("01");
        userKyc.setUserInfoId(user.getUserWrapper().getUser().getUserInfoId());
        // KYCステータス
        userKyc.setKycType(latestUserKyc.getKycType());
        // 制裁チェック
        userKyc.setAntisocialStatus(latestUserKyc.getAntisocialStatus());
        // 承認/否認メール送信日時
        userKyc.setMailSendAt(latestUserKyc.getMailSendAt());
        // eKYC/ファイルアップロード
        userKyc.setKycType(latestUserKyc.getKycType());
        // メール送信状況
        userKyc.setKycMailStatus(latestUserKyc.getKycMailStatus());
        // 審査コメント
        userKyc.setJudgingComment(latestUserKyc.getJudgingComment());
        // AML/CFT用コメント
        userKyc.setAmlCftComment(latestUserKyc.getAmlCftComment());
        userKycService.save(userKyc);
        // 不備/謝絶理由
        List<UserKycSub> userKycSubList = userKycSubService.findByKycId(latestUserKyc.getId());
        for (UserKycSub userKycSub : userKycSubList) {
            UserKycSub userKycSubNew = new UserKycSub();
            userKycSubNew.setKycSubStatus(userKycSub.getKycSubStatus());
            userKycSubNew.setKycId(userKyc.getId());
            userKycSubService.save(userKycSubNew);
        }
        userForUpdate.setUserKycId(userKyc.getId());
        userForUpdate.setEmail(form.getEmail());
        userService.saveWithAuthenticationPrincipal(userForUpdate);
        return ResponseEntity.ok().build();
    }

    private void validatePassword(User user, UserPasswordOtpauthPutForm form) throws Exception {
        if (!ApplicationConfig.validatePassword(form.getOldPassword())
                || !ApplicationConfig.validatePassword(form.getNewPassword())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_PASSWORD_PATTERN);
        }
        if (!globalAuthenticationConfig.matchesBCryptPassword(
                form.getOldPassword(), user.getPassword())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER_PASSWORD);
        }
        if (form.getOldPassword().equals(form.getNewPassword())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_SPECIFY_DIFFERENT_PASSWORD);
        }
    }

    @PutMapping("/password/otpauth")
    public ResponseEntity<MfaTypeData> putPasswordOtpauth(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody UserPasswordOtpauthPutForm form)
            throws Exception {
        User userForUpdate = userService.findOne(user.getId());
        validatePassword(userForUpdate, form);
        return mfaManager.responseMfaTypeData(user.getId(), user.getEmail());
    }

    @PutMapping("/passwordchange")
    public ResponseEntity<Object> putPassword(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody UserPasswordPutForm form)
            throws Exception {
        User userForUpdate = userService.findOne(user.getId());
        validatePassword(userForUpdate, form);
        mfaManager.authenticate(userForUpdate.getId(), userForUpdate.getEmail(), form.getMfaCode());
        userForUpdate.setPassword(
                globalAuthenticationConfig.getBcryptHashpw(form.getNewPassword()));
        userService.saveWithAuthenticationPrincipal(userForUpdate);
        return ResponseEntity.ok().build();
    }

    private User validateResetPassword(String email) throws Exception {
        User user = userService.loadUserByUsername(email);

        if (user == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_USER_IS_NULL);
        }

        return user;
    }

    @PutMapping("/reset-password/otpauth")
    public ResponseEntity<MfaTypeData> putResetPasswordOtpauth(@Valid @RequestBody EmailForm form)
            throws Exception {
        User user = validateResetPassword(form.getEmail());
        if (KycStatus.WAITING_SET_PWD.equals(user.getKycStatus())) {
            String token = redisManager.get("register:code:" + user.getEmail());
            if (token != null) {
                throw new CustomException(
                        ErrorCode.REQUEST_ERROR_RESET_PASSWORD_IS_WAITING_SET_PWD);
            } else {
                throw new CustomException(ErrorCode.REQUEST_ERROR_EXPIRE_TOKEN);
            }
        }
        List<UserMfa> userMfaList = userMfaService.findByCondition(user.getId());
        if (!CollectionUtils.isEmpty(userMfaList)) {
            boolean isGoogle =
                    userMfaList.stream()
                            .anyMatch(
                                    userMfa ->
                                            userMfa.getMfaType().equals(MfaType.GOOGLE)
                                                    && userMfa.isAuthenticated());
            if (isGoogle) {
                return mfaManager.responseMfaTypeData(form.getEmail(), MfaType.GOOGLE);
            }
            boolean isSMS =
                    userMfaList.stream()
                            .anyMatch(
                                    userMfa ->
                                            userMfa.getMfaType().equals(MfaType.SMS)
                                                    && userMfa.isAuthenticated());
            if (isSMS) {
                return mfaManager.responseMfaTypeData(form.getEmail(), MfaType.SMS);
            }
        }
        return mfaManager.responseMfaTypeData(form.getEmail(), MfaType.EMAIL);
    }

    @PutMapping("/reset-password")
    public ResponseEntity<Object> putResetPassword(@Valid @RequestBody ResetPasswordPutForm form)
            throws Exception {
        User userForUpdate = validateResetPassword(form.getEmail());
        mfaManager.authenticate(userForUpdate.getId(), userForUpdate.getEmail(), form.getMfaCode());
        userForUpdate.setPassword(globalAuthenticationConfig.getBcryptHashpw(form.getPassword()));
        userService.saveWithAuthenticationPrincipal(userForUpdate);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/anti-phishing-code/otpauth")
    public ResponseEntity<MfaTypeData> putAntiPhishingCodeOtpauth(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody UserAntiPhishingCodeOtpauthPutForm form)
            throws Exception {
        return mfaManager.responseMfaTypeData(user.getId(), user.getEmail());
    }

    @PutMapping("/anti-phishing-code")
    public ResponseEntity<Object> putAntiPhishingCode(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody UserAntiPhishingCodePutForm form)
            throws Exception {
        mfaManager.authenticate(user.getId(), user.getEmail(), form.getMfaCode());
        User userForUpdate = userService.findOne(user.getId());
        userForUpdate.setAntiPhishingCode(form.getAntiPhishingCode());
        userService.saveWithAuthenticationPrincipal(userForUpdate);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/condition")
    public ResponseEntity<UserConditionData> getCondition(
            @AuthenticationPrincipal UserPrincipal loginUser) {
        User user = userService.findOne(loginUser.getId());
        UserConditionData userConditionData = new UserConditionData();
        userConditionData.setUser(userService.findOne(user.getId()));

        UserAuthority authority = userAuthorityService.findByUserId(user.getId());
        userConditionData.setAuthority(authority.getAuthority());
        userConditionData.setUserMailnoticesResponseList(
                userMailNoticesOffService.findByUserId(user.getId()));
        if (authority.getAuthority().contains(Authority.PERSONAL.toString())) {
            UserInfo userInfo = userInfoService.findOne(user.getUserInfoId());
            if (userInfo != null) {
                userConditionData.setCountry(userInfo.getCountry());
                userConditionData.setSmsPhoneNumber(userInfo.getPhoneNumber());
                userConditionData.setProfileRegistered(true);
            }
        }

        userConditionData.userHistories(userLoginInfoService.findByCondition(user.getId(), 0, 10));

        for (UserMfa userMfa : userMfaService.findByCondition(user.getId())) {
            if (userMfa.getMfaType() == MfaType.GOOGLE && !userMfa.isAuthenticated()) {
                continue;
            }

            userConditionData.addMfaType(userMfa.getMfaType());
        }

        if (user.getAuthorities().get(0).isPersonal()
                && KycStatus.DOCUMENT_RECEIVED.equals(user.getKycStatus())
                && !userEkycService.hasValidEkycUrl(user.getId())) {
            userConditionData.setKycStatus(KycStatus.URL_EXPIRED);
        }
        return ResponseEntity.ok(userConditionData);
    }

    @GetMapping("/level")
    public ResponseEntity<UserLevelData> getLevel(@AuthenticationPrincipal UserPrincipal user) {
        UserLevelData userLevelData = UserLevelService.set(user.getUserWrapper().getUser());
        return ResponseEntity.ok(userLevelData);
    }

    @GetMapping("/agreement")
    public ResponseEntity<List<UserAgreement>> getUserAgreement(
            @AuthenticationPrincipal UserPrincipal user) {
        List<UserAgreement> agreementList = userAgreementService.findByUserId(user.getId());
        return ResponseEntity.ok(agreementList);
    }

    @PostMapping("/agreement")
    public ResponseEntity<UserAgreement> post(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @RequestBody UserAgreementForm form) {
        return ResponseEntity.ok(
                userAgreementService
                        .saveOrUpdateVersion(
                                user.getId(), form.getUserAgreementType(), form.getVersion())
                        .get());
    }
}
