package point.app.invest.controller;

import io.swagger.v3.oas.annotations.Hidden;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.component.CsvDownloadManager;
import point.common.constant.ErrorCode;
import point.common.constant.FiatDepositStatus;
import point.common.constant.FiatWithdrawalStatus;
import point.common.constant.HistoryType;
import point.common.constant.ViewVariables;
import point.common.entity.FiatDeposit;
import point.common.entity.FiatWithdrawal;
import point.common.exception.CustomException;
import point.common.model.response.JpyHistoryData;
import point.common.model.response.JpyHistoryData.JpyHistoryElement;
import point.common.model.response.JpyHistoryReportData;
import point.common.model.response.PageData;
import point.common.service.FiatDepositService;
import point.common.service.FiatWithdrawalService;
import point.common.service.UserService;
import point.common.util.DateUnit;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/jpy-history")
public class V1JpyHistoryRestController {

    private final FiatWithdrawalService fiatWithdrawalService;
    private final FiatDepositService fiatDepositService;
    private final UserService userService;
    private final CsvDownloadManager<JpyHistoryReportData> downloadManager;

    static class SortByDate implements Comparator<JpyHistoryData.JpyHistoryElement> {
        @Override
        public int compare(JpyHistoryData.JpyHistoryElement a, JpyHistoryData.JpyHistoryElement b) {
            return b.getDate().compareTo(a.getDate());
        }
    }

    @GetMapping
    public ResponseEntity<PageData<JpyHistoryElement>> get(
            @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "historyType", required = false) HistoryType historyType,
            @RequestParam(value = "depositStatus", required = false)
                    FiatDepositStatus depositStatus,
            @RequestParam(value = "withdrawalStatus", required = false)
                    FiatWithdrawalStatus paramWithdrawalStatus,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        JpyHistoryData jpyHistoryData = new JpyHistoryData();
        Long count = Long.valueOf(0);

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        if (historyType == HistoryType.DEPOSIT || historyType == null) {
            List<FiatDepositStatus> fiatDepositStatusList = new ArrayList<>();
            if (depositStatus == null) {
                // customer screen displaying done
                fiatDepositStatusList.add(FiatDepositStatus.DONE);
                // fiatDepositStatusList.add(FiatDepositStatus.REJECTED);
            } else {
                fiatDepositStatusList.add(depositStatus);
            }
            List<FiatDeposit> fiatDepositList =
                    new ArrayList<FiatDeposit>(
                            fiatDepositService.findByCondition(
                                    user.getId(),
                                    dateFrom,
                                    dateTo,
                                    null,
                                    null,
                                    fiatDepositStatusList,
                                    0,
                                    Integer.MAX_VALUE));

            jpyHistoryData.setFiatDeposit(fiatDepositList);
        }

        if (historyType == HistoryType.WITHDRAWAL || historyType == null) {
            final var withdrawalStatuses = new ArrayList<FiatWithdrawalStatus>();
            if (paramWithdrawalStatus != null) {
                withdrawalStatuses.addAll(
                        paramWithdrawalStatus.isPending
                                ? FiatWithdrawalStatus.getPendingList()
                                : List.of(paramWithdrawalStatus));
            }
            List<FiatWithdrawal> fiatWithdrawalList =
                    new ArrayList<FiatWithdrawal>(
                            fiatWithdrawalService.findByCondition(
                                    user.getId(),
                                    dateFrom,
                                    dateTo,
                                    null,
                                    null,
                                    withdrawalStatuses,
                                    0,
                                    Integer.MAX_VALUE));

            jpyHistoryData.setFiatWithdrawal(fiatWithdrawalList);
        }

        List<JpyHistoryData.JpyHistoryElement> jpyHistoryElements =
                new ArrayList<JpyHistoryData.JpyHistoryElement>(jpyHistoryData.getJpyHistories());
        Collections.sort(jpyHistoryElements, new SortByDate());

        if (jpyHistoryElements != null) {
            count += jpyHistoryElements.size();
        }

        return ResponseEntity.ok(createPageData(jpyHistoryElements, count, number, size));
    }

    private PageData<JpyHistoryElement> createPageData(
            List<JpyHistoryElement> content, Long count, Integer number, Integer size) {
        List<JpyHistoryElement> pageContents = new ArrayList<JpyHistoryElement>();

        int maxSize =
                (number * size + size) > content.size() ? content.size() : (number * size + size);

        for (int i = number * size; i < maxSize; i++) {
            pageContents.add(content.get(i));
        }

        return new PageData<JpyHistoryElement>(number, size, count, pageContents);
    }

    @GetMapping("/download")
    public String download(
            HttpServletResponse response,
            @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "historyType", required = false) HistoryType historyType,
            @RequestParam(value = "depositStatus", required = false)
                    FiatDepositStatus depositStatus,
            @RequestParam(value = "withdrawalStatus", required = false)
                    FiatWithdrawalStatus paramWithdrawalStatus)
            throws Exception {
        if (userService.findOne(user.getId()) == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
        }

        JpyHistoryData jpyHistoryData = new JpyHistoryData();

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        if (historyType == HistoryType.DEPOSIT || historyType == null) {
            List<FiatDepositStatus> fiatDepositStatusList = new ArrayList<>();
            if (depositStatus != null) {
                fiatDepositStatusList.add(FiatDepositStatus.DONE);
                fiatDepositStatusList.add(FiatDepositStatus.REJECTED);
            }
            List<FiatDeposit> fiatDepositList =
                    new ArrayList<FiatDeposit>(
                            fiatDepositService.findByCondition(
                                    user.getId(),
                                    dateFrom,
                                    dateTo,
                                    null,
                                    null,
                                    fiatDepositStatusList,
                                    0,
                                    Integer.MAX_VALUE));
            jpyHistoryData.setFiatDeposit(fiatDepositList);
        }

        if (historyType == HistoryType.WITHDRAWAL || historyType == null) {
            final var withdrawalStatuses = new ArrayList<FiatWithdrawalStatus>();
            if (paramWithdrawalStatus != null) {
                withdrawalStatuses.addAll(
                        paramWithdrawalStatus.isPending
                                ? FiatWithdrawalStatus.getPendingList()
                                : List.of(paramWithdrawalStatus));
            }
            List<FiatWithdrawal> fiatWithdrawalList =
                    new ArrayList<FiatWithdrawal>(
                            fiatWithdrawalService.findByCondition(
                                    user.getId(),
                                    dateFrom,
                                    dateTo,
                                    null,
                                    null,
                                    withdrawalStatuses,
                                    0,
                                    Integer.MAX_VALUE));
            jpyHistoryData.setFiatWithdrawal(fiatWithdrawalList);
        }

        List<JpyHistoryData.JpyHistoryElement> jpyHistoryElements =
                new ArrayList<JpyHistoryData.JpyHistoryElement>(jpyHistoryData.getJpyHistories());
        Collections.sort(jpyHistoryElements, new SortByDate());

        List<JpyHistoryReportData> jpyHistoryReports = new ArrayList<JpyHistoryReportData>();

        for (JpyHistoryElement jpyHistoryElement : jpyHistoryElements) {
            jpyHistoryReports.add(new JpyHistoryReportData().setProperties(jpyHistoryElement));
        }

        String reportPreFix =
                "jpy_report_"
                        + FormatUtil.formatJst(new Date(dateFrom), FormatPattern.YYYYMMDD)
                        + "-"
                        + (dateTo != null
                                ? FormatUtil.formatJst(new Date(dateTo), FormatPattern.YYYYMMDD)
                                : "");

        downloadManager.download(response, jpyHistoryReports, reportPreFix, true);

        return null;
    }
}
