package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.Country;
import point.common.controller.AbstractRestController;
import point.common.model.response.CountryResponse;

@Hidden
@RequestMapping("/app/v1/country")
@RequiredArgsConstructor
@RestController
@Timed
public class V1CountryRestController extends AbstractRestController {

    @GetMapping
    public ResponseEntity<CountryResponse> get(HttpServletResponse response) {
        setCacheControlForPublic(response);
        return ResponseEntity.ok(new CountryResponse(Country.values()));
    }
}
