package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.IsoFields;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.constant.*;
import point.common.entity.MonsterBase;
import point.common.entity.MonsterFood;
import point.common.entity.UserMonsterInfo;
import point.common.model.response.GlobalApiResponse;
import point.common.model.response.MonsterResponse;
import point.common.service.MonsterBaseService;
import point.common.service.MonsterFoodService;
import point.common.service.UserMonsterInfoService;
import point.pos.entity.PosTrade;
import point.pos.service.PosTradeService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/invest/monster")
@Timed
public class MonsterInvestController {

    private final UserMonsterInfoService userMonsterInfoService;
    private final MonsterBaseService monsterBaseService;
    private final MonsterFoodService monsterFoodService;
    private final PosTradeService posTradeService;

    @GetMapping("/growth-history")
    @Operation(
            summary = "get Operate Monster",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "200",
                        description = "Success"),
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "400",
                        description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<MonsterResponse>> get(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user)
            throws Exception {
        MonsterResponse monsterResponse = new MonsterResponse();
        monsterResponse.setUserId(user.getId());

        UserMonsterInfo userMonsterInfo = userMonsterInfoService.findByUserId(user.getId());
        if (Objects.isNull(userMonsterInfo)) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    400, ErrorCode.COMMON_NO_VALID_DATA_FOUND.getMessage()));
        }
        MonsterBase monsterBase = monsterBaseService.findOne(userMonsterInfo.getMonsterId());
        monsterResponse.setMonsterName(monsterBase.getName());
        monsterResponse.setMonsterImgUrl(monsterBase.getImgUrl());
        monsterResponse.setMonsterLevel(userMonsterInfo.getLevel());
        monsterResponse.setCurrentExperience(userMonsterInfo.getCurrentExperience());
        monsterResponse.setNextExperience(userMonsterInfo.getNextLevelExperience());
        monsterResponse.setMonthlyPower(userMonsterInfo.getMonthlyPower());
        Date creationDateTime = userMonsterInfo.getCreationDateTime();
        if (creationDateTime != null) {
            LocalDate creationDate =
                    creationDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            int year = creationDate.getYear();
            int weekOfYear = creationDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);

            MonsterFood monsterFood =
                    monsterFoodService.findByYearAndWeekOfYear(UserIdType.Invest, year, weekOfYear);
            if (monsterFood != null) {
                monsterResponse.setThisWeekfood(monsterFood.getSymbolId().toString());

                List<PosTrade> posTrades =
                        posTradeService.findByUserIdAndOrderSide(user.getId(), OrderSide.SELL);

                // symbolIdでグループ化し、各グループの経験値の合計を計算します。
                Map<Long, Long> symbolIdToTotalExperience = new HashMap<>();
                Map<Long, List<FoodDetails>> symbolIdToFoodDetails = new HashMap<>();
                for (PosTrade posTrade : posTrades) {
                    Long symbolId = posTrade.getSymbolId();
                    long experiencePoints = posTrade.getExperiencePoints();
                    symbolIdToTotalExperience.put(
                            symbolId,
                            symbolIdToTotalExperience.getOrDefault(symbolId, 0L)
                                    + experiencePoints);

                    FoodDetails foodDetails = new FoodDetails();
                    foodDetails.setTradingHours(posTrade.getCreatedAt());
                    BigDecimal profitAndLoss =
                            posTrade.getIncome() != null
                                    ? posTrade.getIncome().setScale(2, RoundingMode.HALF_UP)
                                    : null;
                    foodDetails.setRealizedProfitAndLoss(profitAndLoss);
                    foodDetails.setExperienceGained(experiencePoints);
                    foodDetails.setTreeLevel(posTrade.getUserGrowthStageId().toString());

                    symbolIdToFoodDetails
                            .computeIfAbsent(symbolId, k -> new ArrayList<>())
                            .add(foodDetails);
                }

                List<MonsterFoodCourse> monsterCourseList = new ArrayList<>();
                for (Map.Entry<Long, Long> entry : symbolIdToTotalExperience.entrySet()) {
                    MonsterFoodCourse monsterFoodCourse = new MonsterFoodCourse();
                    monsterFoodCourse.setCourse(
                            CurrencyTypeMonster.getMonsterCurrencyTypeById(entry.getKey()));
                    monsterFoodCourse.setTotalExperience(entry.getValue().toString());
                    monsterFoodCourse.setFoodDetailsList(symbolIdToFoodDetails.get(entry.getKey()));
                    monsterCourseList.add(monsterFoodCourse);
                }
                monsterResponse.setMonsterCourseList(monsterCourseList);
            }
        }

        return ResponseEntity.ok(GlobalApiResponse.success(monsterResponse));
    }
}
