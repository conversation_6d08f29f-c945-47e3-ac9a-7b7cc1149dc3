package point.app.invest.controller;

import io.swagger.v3.oas.annotations.Hidden;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.app.config.PostcodeConfig;
import point.common.constant.ErrorCode;
import point.common.exception.CustomException;
import point.common.model.response.ZipCodeData;
import point.common.service.ZipCodeService;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/postcodes")
public class V1PostcodesRestController {

    private final PostcodeConfig postcodeConfig;

    private final ZipCodeService zipCodeService;

    @GetMapping
    public String get(
            @AuthenticationPrincipal UserPrincipal user,
            @RequestParam(value = "postcode", required = true) String postcode)
            throws Exception {
        HttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet request = new HttpGet(postcodeConfig.getUrl() + "/" + postcode);
        request.addHeader("apikey", postcodeConfig.getApikey());
        request.addHeader("content-type", "application/json");
        HttpResponse response = httpClient.execute(request);
        String result = IOUtils.toString(response.getEntity().getContent(), "UTF-8");
        return result;
    }

    @GetMapping("/zipcode")
    public ResponseEntity<Map<String, Object>> getZipCode(
            @RequestParam(value = "postcode", required = true) String postcode)
            throws CustomException {
        Map<String, Object> response = new HashMap<>();

        if (!StringUtils.hasText(postcode)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_ZIPCODE_NOTNULL);
        }
        ZipCodeData zipCodeData =
                zipCodeService
                        .search(postcode)
                        .orElseThrow(() -> new CustomException(ErrorCode.REQUEST_ERROR_ZIPCODE));
        response.put("zipCode", zipCodeData);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
