package point.app.invest.model;

import java.io.IOException;
import java.io.Serializable;
import java.util.Date;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.security.core.context.SecurityContextHolder;
import point.app.component.model.UserPrincipal;
import point.app.invest.model.request.UserAntiPhishingCodeOtpauthPutForm;
import point.app.invest.model.request.UserAntiPhishingCodePutForm;
import point.app.invest.model.request.UserPasswordOtpauthPutForm;
import point.app.invest.model.request.UserPasswordPutForm;
import point.common.util.JsonUtil;

@NoArgsConstructor
public class UserLog implements Serializable {

    private static final long serialVersionUID = -3841078657722021328L;

    @Getter @Setter private Long userId;
    @Getter @Setter private String activity;
    @Getter @Setter private String httpUrl;
    @Getter @Setter private String httpMethod;
    @Getter @Setter private Integer httpStatus;
    @Getter @Setter private Date createdAt;

    public UserLog(HttpServletRequest request, HttpServletResponse response) throws IOException {
        UserPrincipal user =
                (UserPrincipal)
                        SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (user != null) {
            userId = user.getId();
        }

        httpUrl = request.getRequestURI();
        httpMethod = request.getMethod();
        httpStatus = response.getStatus();
        createdAt = new Date();

        if (httpMethod.toLowerCase().equals("get") || httpMethod.toLowerCase().equals("delete")) {
            activity = getActivityFromParameter(request);
        } else {
            activity = getActivityFromBody(request);
        }
    }

    private String getActivityFromBody(HttpServletRequest request) throws IOException {
        String activity = request.getReader().lines().collect(Collectors.joining(""));
        if (request.getRequestURI().endsWith("/user/password/otpauth")) {
            UserPasswordOtpauthPutForm form =
                    JsonUtil.decode(activity, UserPasswordOtpauthPutForm.class);
            form.setNewPassword("");
            form.setOldPassword("");
            activity = JsonUtil.encode(form);
        }
        if (request.getRequestURI().endsWith("/user/passwordchange")) {
            UserPasswordPutForm form = JsonUtil.decode(activity, UserPasswordPutForm.class);
            form.setNewPassword("");
            form.setOldPassword("");
            activity = JsonUtil.encode(form);
        }
        if (request.getRequestURI().endsWith("/user/anti-phishing-code/otpauth")) {
            UserAntiPhishingCodeOtpauthPutForm form =
                    JsonUtil.decode(activity, UserAntiPhishingCodeOtpauthPutForm.class);
            form.setAntiPhishingCode("");
            activity = JsonUtil.encode(form);
        }
        if (request.getRequestURI().endsWith("/user/anti-phishing-code")) {
            UserAntiPhishingCodePutForm form =
                    JsonUtil.decode(activity, UserAntiPhishingCodePutForm.class);
            form.setAntiPhishingCode("");
            activity = JsonUtil.encode(form);
        }
        return activity;
    }

    private String getActivityFromParameter(HttpServletRequest request) throws IOException {
        return JsonUtil.encode(request.getParameterMap());
    }
}
