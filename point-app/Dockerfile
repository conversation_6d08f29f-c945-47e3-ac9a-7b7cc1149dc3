FROM gradle:7.0.0-jdk16 as builder
COPY --chown=gradle:gradle ./build.gradle /home/<USER>/
COPY --chown=gradle:gradle ./src /home/<USER>/src
RUN gradle build -Pbuilddir=build -x test -i

FROM openjdk:16
ARG ENVIRONMENT
ENV ENVIRONMENT $ENVIRONMENT
RUN mkdir -p /app/log
WORKDIR /app
COPY --from=builder /home/<USER>/build/libs/point-app.jar /app
ENTRYPOINT [ "sh", "-c", "java -jar /app/point-app.jar --spring.profiles.active=$ENVIRONMENT" ]
