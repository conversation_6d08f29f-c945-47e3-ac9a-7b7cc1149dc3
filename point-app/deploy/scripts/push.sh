#!/bin/bash

ENVIRONMENT=
BUILD_DOCKER_IMAGE_VERSION_ARG=

# parse args
while true; do
  if [ -z $1 ]; then
    break
  fi
  case $1 in
    "-v") shift; BUILD_DOCKER_IMAGE_VERSION_ARG=$1 ;;
    *)
      if [ -z $ENVIRONMENT ]; then
        ENVIRONMENT=$1
      fi
      ;;
  esac
  shift
done

# validate
if [ -z $ENVIRONMENT ]; then
  cat <<EOF
usage: $0 <environment>
EOF
  exit 1
fi

# load env file
envfile=deploy/env/$ENVIRONMENT

if [ -f $envfile ]; then
  source $envfile
fi

# update env
if [ ! -z $BUILD_DOCKER_IMAGE_VERSION_ARG ]; then
  BUILD_DOCKER_IMAGE_VERSION=$BUILD_DOCKER_IMAGE_VERSION_ARG
fi

# docker push
URI=$BUILD_DOCKER_IMAGE_NAME:$BUILD_DOCKER_IMAGE_VERSION
echo "docker push $URI"
docker push $URI
