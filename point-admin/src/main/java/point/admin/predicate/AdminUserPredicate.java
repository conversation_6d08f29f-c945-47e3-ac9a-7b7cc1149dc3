package point.admin.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.admin.entity.AdminUser;
import point.admin.entity.AdminUser_;
import point.common.predicate.EntityPredicate;

@Component
public class AdminUserPredicate extends EntityPredicate<AdminUser> {

    public Predicate equalEmail(
            CriteriaBuilder criteriaBuilder, Root<AdminUser> root, String email) {
        return criteriaBuilder.equal(root.get(AdminUser_.email), email);
    }
}
