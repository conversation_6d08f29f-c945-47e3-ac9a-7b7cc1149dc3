package point.admin.config;

import java.io.IOException;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.http.entity.ContentType;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfFilter;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.OncePerRequestFilter;
import point.admin.model.request.MultiReadHttpServletRequest;
import point.common.constant.Security;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    private final AccessDeniedHandler accessDeniedHandler;

    private final AuthenticationEntryPoint authenticationEntryPoint;

    private final AuthenticationFailureHandler authenticationFailureHandler;

    private final AuthenticationSuccessHandler authenticationSuccessHandler;

    private final LogoutHandler logoutHandler;

    private final LogoutSuccessHandler logoutSuccessHandler;

    private final SessionCookieConfig sessionCookieConfig;

    private CsrfTokenRepository csrfTokenRepository() {
        CookieCsrfTokenRepository repository = CookieCsrfTokenRepository.withHttpOnlyFalse();

        if (Boolean.TRUE.equals(sessionCookieConfig.getSecure())) {
            repository.setSecure(true);
        }

        return repository;
    }

    private Filter csrfHeaderFilter() {
        return new OncePerRequestFilter() {
            @Override
            protected void doFilterInternal(
                    HttpServletRequest request,
                    HttpServletResponse response,
                    FilterChain filterChain)
                    throws ServletException, IOException {
                // cached requestBody
                MultiReadHttpServletRequest multiReadRequest =
                        new MultiReadHttpServletRequest(request);
                response.setContentType(ContentType.APPLICATION_JSON.toString());
                filterChain.doFilter(multiReadRequest, response);
            }
        };
    }

    private CorsConfigurationSource corsConfigurationSource() {
        // API が同一ドメインとし、CORS が発生しない想定。
        // なのでバックエンド側では全ドメイン許可に設定しておく。
        // 特定ドメインでしかバックエンドを稼働できないよう構成する場合は CORS を設定する。

        CorsConfiguration corsConfiguration = new CorsConfiguration();

        corsConfiguration.addAllowedMethod(CorsConfiguration.ALL);
        corsConfiguration.addAllowedHeader(CorsConfiguration.ALL);
        corsConfiguration.addAllowedOriginPattern(CorsConfiguration.ALL);
        corsConfiguration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource corsConfigurationSource =
                new UrlBasedCorsConfigurationSource();
        corsConfigurationSource.registerCorsConfiguration("/**", corsConfiguration);

        return corsConfigurationSource;
    }

    @Override
    public void configure(WebSecurity web) {
        web.ignoring()
                .antMatchers(
                        "/webjars/**",
                        "/css/**",
                        "/js/**",
                        "/favicon.ico",
                        "/admin/v1/paypay-deposit",
                        "/swagger-ui.html",
                        "/v2/api-docs", // swagger api json
                        "/swagger-resources/configuration/ui",
                        "/swagger-resources",
                        "/swagger-resources/configuration/security",
                        "/swagger-resources/**",
                        "/admin/v1/operate/order/allForPos",
                        "/webjars/**");
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
                .mvcMatchers(
                        "/",
                        "/actuator/**",
                        "/healthcheck",
                        "/admin/healthcheck",
                        "/admin/v1/register",
                        "/admin/v1/login",
                        "/admin/v1/logout",
                        "/admin/v1/gmo/authorization")
                .permitAll()
                .anyRequest()
                .authenticated()
                .and()
                .exceptionHandling()
                .authenticationEntryPoint(authenticationEntryPoint)
                .accessDeniedHandler(accessDeniedHandler)
                .and()
                .formLogin()
                .loginProcessingUrl("/admin/v1/login")
                .permitAll()
                .usernameParameter("email")
                .passwordParameter("password")
                .successHandler(authenticationSuccessHandler)
                .failureHandler(authenticationFailureHandler)
                .and()
                .logout()
                .logoutUrl("/admin/v1/logout")
                .addLogoutHandler(logoutHandler)
                .invalidateHttpSession(true)
                .deleteCookies(Security.XSRF_TOKEN)
                .deleteCookies(Security.JSESSIONID)
                .logoutSuccessHandler(logoutSuccessHandler)
                .and()
                .csrf()
                .ignoringAntMatchers(
                        "/actuator/**",
                        "/admin/v1/register",
                        "/admin/v1/login",
                        "/admin/v1/logout",
                        "/admin/v1/gmo/authorization")
                .csrfTokenRepository(csrfTokenRepository())
                .and()
                .cors()
                .configurationSource(corsConfigurationSource())
                .and()
                .addFilterAfter(csrfHeaderFilter(), CsrfFilter.class);
        http.sessionManagement()
                .maximumSessions(1)
                .maxSessionsPreventsLogin(false)
                .sessionRegistry(sessionRegistry());
    }

    @Bean
    public SessionRegistry sessionRegistry() {
        SessionRegistry sessionRegistry = new SessionRegistryImpl();
        return sessionRegistry;
    }
}
