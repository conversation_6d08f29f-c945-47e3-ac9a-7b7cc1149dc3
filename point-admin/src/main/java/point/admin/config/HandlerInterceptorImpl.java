package point.admin.config;

import java.io.IOException;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import point.admin.model.AdminUserLog;
import point.common.service.LogService;
import point.common.util.JsonUtil;

@RequiredArgsConstructor
@Component
@Slf4j
public class HandlerInterceptorImpl implements HandlerInterceptor {
    public static final String START_DURATION = "startDuration";

    private final LogService logService;

    @Override
    public boolean preHandle(
            HttpServletRequest request, HttpServletResponse response, Object handler)
            throws IOException {
        request.setAttribute(START_DURATION, System.currentTimeMillis());

        logService.logRequestStart(request);

        return true;
    }

    @Override
    public void postHandle(
            HttpServletRequest request,
            HttpServletResponse response,
            Object handler,
            ModelAndView modelAndView) {}

    @Override
    public void afterCompletion(
            HttpServletRequest request, HttpServletResponse response, Object handler, Exception e)
            throws IOException {
        if (request.getRequestURI().indexOf("healthcheck") < 0) {
            log.info(JsonUtil.encode(new AdminUserLog(request, response)));
        }
        log.info(
                "*** end "
                        + request.getRequestURL().toString()
                        + " "
                        + (new Date().getTime() - (long) request.getAttribute(START_DURATION))
                        + " ***");
    }
}
