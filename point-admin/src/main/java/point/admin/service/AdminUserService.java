package point.admin.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import point.admin.entity.AdminUser;
import point.admin.entity.AdminUserAuthority;
import point.admin.entity.AdminUser_;
import point.admin.predicate.AdminUserPredicate;
import point.common.component.QueryExecutorReturner;
import point.common.exception.LoginExcepiton;
import point.common.service.EntityService;

@Slf4j
@Service("auth")
@RequiredArgsConstructor
public class AdminUserService extends EntityService<AdminUser, AdminUserPredicate>
        implements UserDetailsService {

    private final AdminUserLoginAttemptService adminUserLoginAttemptService;
    private final AdminRoleService adminRoleService;
    private final AdminUserAuthorityService adminUserAuthorityService;
    private final String message = "該当するユーザーが所属するロールは廃止されましたため、管理者に連絡してください。";

    @Override
    public Class<AdminUser> getEntityClass() {
        return AdminUser.class;
    }

    @Override
    protected void fetch(Root<AdminUser> root) {
        super.fetch(root);
        root.fetch(AdminUser_.authorities, JoinType.LEFT);
    }

    @Override
    public AdminUser loadUserByUsername(String username)
            throws UsernameNotFoundException, LockedException {
        AdminUser adminUser =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<AdminUser, AdminUser>() {
                            @Override
                            public AdminUser query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.equalEmail(criteriaBuilder, root, username));
                                return getSingleResult(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        if (Objects.isNull(adminUser)) {
            log.info("No user found by username: {}", username);
            throw new UsernameNotFoundException(message);
        }
        AdminUserAuthority authority =
                adminUserAuthorityService.findByAdminUserId(adminUser.getId());
        String roleId = authority.getAuthority(); // role id
        List<String> menus = adminRoleService.getMenuKey(Long.valueOf(roleId));
        adminUser.setRoleId(Long.valueOf(roleId));
        adminUser.setMenus(menus);
        if (adminUser.getRole() == null) {
            // TODO Here, a custom exception class is used to handle login exceptions. However, one
            // issue is that this error will be displayed in the console.
            throw new LoginExcepiton(message);
        }
        if (!adminUser.isAccountNonLocked()
                && !adminUserLoginAttemptService.isLockExpired(adminUser.getId())) {
            throw new LockedException("Login attempts over the limit.");
        }
        return adminUser;
    }

    public boolean check(String... permissions) {
        AdminUser authUser =
                (AdminUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return Arrays.stream(permissions).anyMatch(authUser.getMenus()::contains);
    }

    public AdminUser findByEmail(String email) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public AdminUser query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalEmail(criteriaBuilder, root, email));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
