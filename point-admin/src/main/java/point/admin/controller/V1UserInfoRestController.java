package point.admin.controller;

import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.request.UserInfoWithUserIdForm;
import point.common.constant.ErrorCode;
import point.common.entity.User;
import point.common.entity.UserInfo;
import point.common.exception.CustomException;
import point.common.service.UserInfoService;
import point.common.service.UserService;
import point.common.util.BindingResultUtil;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/user-info")
public class V1UserInfoRestController extends ExchangeAdminController {

    private final UserService userService;
    private final UserInfoService userInfoService;

    @GetMapping
    public ResponseEntity<UserInfo> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "userId", required = false) Long userId)
            throws Exception {

        Long searchId;
        if (userId != null) {
            User user = userService.findOne(userId);
            searchId = user.getUserInfoId();
        } else {
            searchId = id;
        }

        UserInfo userInfo = userInfoService.findOne(searchId);

        return ResponseEntity.ok(userInfo);
    }

    @PostMapping
    public ResponseEntity<Object> post(
            @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody UserInfoWithUserIdForm form,
            BindingResult result)
            throws Exception {

        User user = userService.findOne(form.getUserId());
        if (user == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
        }

        // validation
        BindingResultUtil bindingResultUtil = new BindingResultUtil();
        if (bindingResultUtil.hasErrors(result)) {
            return ResponseEntity.badRequest().body(bindingResultUtil.getErrorList());
        }

        UserInfo userInfo = new UserInfo(user.getId());
        userInfo.setInsider(form.getInsider());
        userInfo = userInfoService.save(userInfo.setProperties(form));
        user.setUserInfoId(userInfo.getId());
        user.setInsider(form.getInsider());
        if (userInfo.getOccupation() == 9
                || userInfo.getOccupation() == 10
                || userInfo.getOccupation() == 11) {
            form.setPosition(null);
            form.setWorkPlace(null);
            userInfoService.save(userInfo.setProperties(form));
        } else {
            form.setPriceFrom(null);
            userInfoService.save(userInfo.setProperties(form));
        }
        if (!(userInfo.getOccupation() == 1
                || userInfo.getOccupation() == 2
                || userInfo.getOccupation() == 7)) {
            form.setIndustry(null);
            userInfoService.save(userInfo.setProperties(form));
        }
        userService.save(user);
        return ResponseEntity.ok(userInfo);
    }
}
