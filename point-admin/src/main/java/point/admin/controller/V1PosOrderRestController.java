package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.component.CsvDownloadManager;
import point.common.constant.CurrencyPair;
import point.common.constant.OrderChannel;
import point.common.constant.OrderSide;
import point.common.constant.OrderStatus;
import point.common.constant.OrderType;
import point.common.constant.PosOrderStatus;
import point.common.constant.ViewVariables;
import point.common.entity.Symbol;
import point.common.model.response.PageData;
import point.common.model.response.PosOrderReportData;
import point.common.model.response.PosOrderTableData;
import point.common.service.SymbolService;
import point.pos.entity.PosOrder;
import point.pos.service.PosOrderService;

@Slf4j
@Timed
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/v1/pos/order")
public class V1PosOrderRestController extends ExchangeAdminController {

    private final SymbolService symbolService;
    private final CsvDownloadManager<PosOrderReportData> downloadManager;
    private final PosOrderService posOrderService;

    @GetMapping("/allForPos")
    public ResponseEntity<PageData<PosOrderTableData>> getAllForPos(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "symbolId") Long symbolId,
            @RequestParam(value = "userIds", required = false) List<Long> userIds,
            @RequestParam(value = "exceptUserIds", required = false) List<Long> exceptUserIds,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "orderStatus", required = false) OrderStatus orderStatus,
            @RequestParam(value = "orderType", required = false) OrderType orderType,
            @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
            @RequestParam(value = "exceptOrderTypes", required = false)
                    List<OrderType> exceptOrderTypes,
            @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
            @RequestParam(value = "orderChannel", required = false) OrderChannel orderChannel,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        PageData<PosOrderTableData> pg = new PageData(number, size, 0, null);
        PageData<PosOrderTableData> temp = new PageData(number, size, 0, null);

        if (symbolId == null) {
            return ResponseEntity.ok(pg);
        }

        Symbol symbol = symbolService.findOne(symbolId);

        PageData<PosOrder> data = new PageData<PosOrder>(number, size, 0, null);
        List<PosOrderStatus> posOrderStatuses = new ArrayList<>();
        if (orderStatus != null) {
            if (orderStatus.toString() == PosOrderStatus.FULLY_FILLED.toString()) {
                PosOrderStatus posOrderSts = PosOrderStatus.valueOf(orderStatus.toString());
                posOrderStatuses.add(posOrderSts);
            } else if (orderStatus.toString() == PosOrderStatus.PARTIALLY_FILLED.toString()) {
                PosOrderStatus posOrderSts = PosOrderStatus.valueOf(orderStatus.toString());
                posOrderStatuses.add(posOrderSts);
            } else {
                posOrderStatuses.add(PosOrderStatus.valueOf(PosOrderStatus.WAITING.toString()));
                posOrderStatuses.add(
                        PosOrderStatus.valueOf(PosOrderStatus.FAILED_FILLED.toString()));
            }
        }

        // 注文一覧取得
        List<PosOrder> posOrdersMain =
                posOrderService.findByConditionForBo(
                        symbolId,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        posOrderStatuses,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        orderSide,
                        orderChannel,
                        number,
                        size,
                        false);

        Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
        Date dateToDate = (dateTo == null) ? null : new Date(dateTo);

        List<PosOrder> posOrderHistoryList =
                posOrderService.findAllFromHistoryForBo(
                        symbol,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFromDate,
                        dateToDate,
                        posOrderStatuses,
                        orderType,
                        orderSide,
                        orderChannel);

        // merge & sort
        posOrdersMain.addAll(posOrderHistoryList);
        posOrdersMain.sort((x, y) -> y.getId().compareTo(x.getId()));
        List<PosOrder> posOrders = posOrdersMain; // ソート処理

        // PageData作成
        // (posOrders + posOrderHistoryList合算後)
        Long count = (long) posOrders.size();
        PageData<PosOrder> dataHistoryList =
                posOrderService.createPageData(posOrders, count, number, size);

        data = dataHistoryList;

        CurrencyPair currencyPair = symbol.getCurrencyPair();
        for (int i = 0; i < data.getContent().size(); i++) {
            PosOrder posOrderData;
            posOrderData = data.getContent().get(i);
            // 桁数揃え
            PosOrderTableData tableData = new PosOrderTableData().setProperties(posOrderData);
            tableData.setAmount(
                    currencyPair.getAmountByTradeType(
                            tableData.getAmount(), symbol.getTradeType()));
            tableData.setPrice(
                    currencyPair.getScaledPrice(tableData.getPrice(), RoundingMode.DOWN));
            tableData.setAveragePrice(
                    currencyPair.getScaledPrice(tableData.getAveragePrice(), RoundingMode.DOWN));
            tableData.setRemainingAmount(
                    currencyPair.getScaledAmount(
                            tableData.getRemainingAmount(), RoundingMode.HALF_UP));
            temp.getContent().add(tableData);
        }
        temp.addTotalElements(data.getTotalElements()); // 全件数を取得するためにループは最後まで回す
        pg =
                new PageData<PosOrderTableData>(
                        number, size, temp.getTotalElements(), temp.getContent());
        return ResponseEntity.ok(pg);
    }

    @GetMapping("/download")
    public String download(
            @AuthenticationPrincipal AdminUser adminUser,
            HttpServletResponse response,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "symbolId") Long symbolId,
            @RequestParam(value = "userIds", required = false) List<Long> userIds,
            @RequestParam(value = "exceptUserIds", required = false) List<Long> exceptUserIds,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "orderStatus", required = false) OrderStatus orderStatus,
            @RequestParam(value = "orderType", required = false) OrderType orderType,
            @RequestParam(value = "orderTypes", required = false) List<OrderType> orderTypes,
            @RequestParam(value = "exceptOrderTypes", required = false)
                    List<OrderType> exceptOrderTypes,
            @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
            @RequestParam(value = "orderChannel", required = false) OrderChannel orderChannel)
            throws Exception {

        List<OrderStatus> orderStatuses = new ArrayList<>();
        if (orderStatus != null) {
            orderStatuses.add(orderStatus);
        }

        List<PosOrderReportData> reportDataList = new ArrayList<PosOrderReportData>();
        Symbol symbol = symbolService.findOne(symbolId);
        if (symbol == null) {
            return null;
        }

        List<PosOrderStatus> posOrderStatuses = new ArrayList<>();
        if (orderStatus != null) {
            if (orderStatus.toString() == PosOrderStatus.FULLY_FILLED.toString()) {
                PosOrderStatus posOrderSts = PosOrderStatus.valueOf(orderStatus.toString());
                posOrderStatuses.add(posOrderSts);
            } else if (orderStatus.toString() == PosOrderStatus.PARTIALLY_FILLED.toString()) {
                PosOrderStatus posOrderSts = PosOrderStatus.valueOf(orderStatus.toString());
                posOrderStatuses.add(posOrderSts);
            } else {
                posOrderStatuses.add(PosOrderStatus.valueOf(PosOrderStatus.WAITING.toString()));
                posOrderStatuses.add(
                        PosOrderStatus.valueOf(PosOrderStatus.FAILED_FILLED.toString()));
            }
        }

        // 注文一覧取得
        List<PosOrder> posOrders =
                posOrderService.findByConditionForBo(
                        symbolId,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        posOrderStatuses,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        orderSide,
                        orderChannel,
                        0,
                        Integer.MAX_VALUE,
                        false);

        if (posOrders == null) {
            posOrders = new ArrayList<>();
        }
        for (PosOrder posOrder : posOrders) {
            reportDataList.add(
                    new PosOrderReportData().setProperties(posOrder, symbol.getCurrencyPair()));
        }

        Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
        Date dateToDate = (dateTo == null) ? null : new Date(dateTo);

        List<PosOrder> posOrderHistoryList =
                posOrderService.findAllFromHistoryForBo(
                        symbol,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFromDate,
                        dateToDate,
                        posOrderStatuses,
                        orderType,
                        orderSide,
                        orderChannel);

        for (PosOrder posOrder : posOrderHistoryList) {
            reportDataList.add(
                    new PosOrderReportData().setProperties(posOrder, symbol.getCurrencyPair()));
        }
        reportDataList.sort((x, y) -> x.getId().compareTo(y.getId()));

        String fileNamePrefix = "posOrder";
        downloadManager.download(
                response, reportDataList, fileNamePrefix, PosOrderReportData.getReportHeader());

        return null;
    }
}
