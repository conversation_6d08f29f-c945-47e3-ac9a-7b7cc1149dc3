package point.admin.controller;

import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.constant.KycStatus;
import point.common.constant.KycSubStatus;
import point.common.exception.CustomException;
import point.common.service.UserKycSubService;

@RequestMapping("/admin/v1/user-kyc-sub")
@RequiredArgsConstructor
@RestController
public class V1UserKycSubRestController extends ExchangeAdminController {

    private final UserKycSubService userKycSubService;

    @GetMapping("statuses")
    public ResponseEntity<List<KycSubStatus>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "kycSubStatus", required = false) KycStatus kycStatus)
            throws CustomException {

        if (kycStatus == null) {
            return ResponseEntity.ok(Arrays.stream(KycSubStatus.values()).toList());
        }
        final var filteringKycSubStatuses =
                Arrays.stream(KycSubStatus.values())
                        .filter(kycSubStatus -> kycSubStatus.parentKycStatus == kycStatus)
                        .toList();
        return ResponseEntity.ok(filteringKycSubStatuses);
    }
}
