package point.admin.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.*;
import point.common.entity.Symbol;
import point.common.exception.CustomException;
import point.common.model.response.OperateCoverOrderTableData;
import point.common.model.response.PageData;
import point.common.service.SymbolService;
import point.operate.entity.OperateCoverOrder;
import point.operate.service.CoverOrderService;
import point.pos.entity.PosMarketMakerConfig;
import point.pos.service.PosMarketMakerConfigService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/operate/cover-order")
public class V1OperateCoverOrderController {

    private final CoverOrderService operateCoverOrderService;
    private final SymbolService symbolService;
    private final PosMarketMakerConfigService posMarketMakerConfigService;

    @GetMapping("/page")
    @PreAuthorize("@auth.check('operate-cover-order')")
    public ResponseEntity<PageData<OperateCoverOrderTableData>> get(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "tradeType") TradeType tradeType,
            @RequestParam(value = "currencyPair") CurrencyPair currencyPair,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "orderType", required = false) OrderType orderType,
            @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
            @RequestParam(value = "greaterThanRemainingAmount", required = false)
                    BigDecimal greaterThanRemainingAmount,
            @RequestParam(value = "lessThanRemainingAmount", required = false)
                    BigDecimal lessThanRemainingAmount,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        PageData<OperateCoverOrderTableData> pg = new PageData<>(number, size, 0, null);
        List<OperateCoverOrderTableData> tableDatas = new ArrayList<>();
        // 注文一覧のため、件数上通貨ペア必須とする
        Symbol symbol = symbolService.findByCondition(tradeType, currencyPair);

        if (symbol == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        PosMarketMakerConfig marketMakerConfig =
                posMarketMakerConfigService.findBySymbolIdAll(symbol.getId());
        if (marketMakerConfig == null) {
            return ResponseEntity.ok(pg);
        }

        PageData<OperateCoverOrder> pageOperateCoverOrders =
                operateCoverOrderService.findByConditionPageData(
                        currencyPair,
                        symbol.getId(),
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo,
                        orderType,
                        orderSide,
                        greaterThanRemainingAmount,
                        lessThanRemainingAmount,
                        number,
                        size);
        List<OperateCoverOrder> operateCoverOrders = pageOperateCoverOrders.getContent();
        if (CollectionUtils.isEmpty(operateCoverOrders)) {
            return ResponseEntity.ok(pg);
        }
        for (OperateCoverOrder operateCoverOrder : operateCoverOrders) {
            String currencyPairName = currencyPair.getName();
            OperateCoverOrderTableData tableData =
                    new OperateCoverOrderTableData()
                            .setProperties(currencyPairName, operateCoverOrder);
            tableDatas.add(tableData);
        }
        return ResponseEntity.ok(
                new PageData<>(
                        number, size, pageOperateCoverOrders.getTotalElements(), tableDatas));
    }
}
