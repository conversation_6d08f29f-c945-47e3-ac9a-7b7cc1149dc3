package point.admin.controller;

import java.io.Serializable;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.constant.ViewVariables;
import point.common.entity.WorkerMaster;
import point.common.model.request.WorkerMasterUpdateForm;
import point.common.service.WorkerMasterService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/worker-master")
public class V1WorkerMasterRestController extends ExchangeAdminController {

    private final WorkerMasterService workerMasterService;

    @GetMapping
    public ResponseEntity<Serializable> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        if (id != null) {
            return ResponseEntity.ok(workerMasterService.findOne(id));
        } else {
            return ResponseEntity.ok(workerMasterService.findAll(number, size, Direction.ASC));
        }
    }

    @PutMapping
    public ResponseEntity<WorkerMaster> put(
            @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody WorkerMasterUpdateForm form)
            throws Exception {

        WorkerMaster workerMaster = workerMasterService.findOne(form.getId());
        workerMaster.setEnabled(form.isEnabled());
        workerMaster.setIntervalMillis(form.getIntervalMillis());
        return ResponseEntity.ok(workerMasterService.save(workerMaster));
    }
}
