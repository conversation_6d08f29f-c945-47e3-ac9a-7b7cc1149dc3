package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.ChoicePowerTransferType;
import point.common.constant.UserIdType;
import point.common.constant.ViewVariables;
import point.common.entity.ChoicePowerTransfer;
import point.common.entity.PointUser;
import point.common.entity.User;
import point.common.entity.UserIdentity;
import point.common.model.dto.PointUserDTO;
import point.common.model.dto.UserDTO;
import point.common.model.dto.UserIdentityDTO;
import point.common.model.response.ChoicePowerTransferData;
import point.common.model.response.PageData;
import point.common.service.ChoicePowerTransferService;
import point.common.service.PointUserService;
import point.common.service.UserIdentityService;
import point.common.service.UserService;

@Timed
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/choice/powerTransfer")
public class V1ChoicePowerTransferController extends ExchangeAdminController {

    public final ChoicePowerTransferService choicePowerTransferService;

    private final UserIdentityService userIdentityService;

    private final PointUserService pointUserService;

    private final UserService userService;

    @GetMapping
    @PreAuthorize("@auth.check('choice-power-transfer-list')")
    public ResponseEntity<PageData<ChoicePowerTransferData>> get(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "idType", required = false) UserIdType idType,
            @RequestParam(value = "transferType", required = false)
                    ChoicePowerTransferType transferType,
            @RequestParam(value = "choiceActivityRuleId", required = false)
                    Long choiceActivityRuleId,
            @RequestParam(value = "partnerMemberId", required = false) String partnerMemberId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size) {
        PageData<ChoicePowerTransferData> response;
        PageData<ChoicePowerTransfer> pageData =
                choicePowerTransferService.findByCondition(
                        userId,
                        idType,
                        transferType,
                        choiceActivityRuleId,
                        partnerMemberId,
                        dateFrom,
                        dateTo,
                        number,
                        size);
        List<ChoicePowerTransferData> dataList = new ArrayList<>();
        for (ChoicePowerTransfer choicePowerTransfer : pageData.getContent()) {
            dataList.add(this.toChoicePowerTransferData(choicePowerTransfer));
        }
        response = new PageData<>(number, size, pageData.getTotalElements(), dataList);

        return ResponseEntity.ok().body(response);
    }

    private ChoicePowerTransferData toChoicePowerTransferData(ChoicePowerTransfer transfer) {
        ChoicePowerTransferData data = new ChoicePowerTransferData();
        data.setId(transfer.getId());
        data.setUserId(transfer.getUserId());
        data.setAmount(transfer.getAmount());
        data.setTransferType(transfer.getTransferType());
        data.setChoiceActivityRuleId(transfer.getChoiceActivityRuleId());
        data.setDescription(transfer.getDescription());
        UserIdentity userIdentity = userIdentityService.findOne(transfer.getUserId());
        if (userIdentity != null) {
            UserIdentityDTO userIdentityDTO = new UserIdentityDTO();
            userIdentityDTO.setId(userIdentity.getId());
            userIdentityDTO.setIdType(userIdentity.getIdType());
            data.setUserIdentity(userIdentityDTO);
        }
        PointUser operatePointUser = pointUserService.findOne(transfer.getUserId());
        if (operatePointUser != null) {
            PointUserDTO pointUserDTO = new PointUserDTO();
            pointUserDTO.setId(operatePointUser.getId());
            pointUserDTO.setUserId(operatePointUser.getUserId());
            pointUserDTO.setPartnerId(operatePointUser.getPartnerId());
            pointUserDTO.setPartnerMemberId(operatePointUser.getPartnerMemberId());
            data.setOperatorPointUser(pointUserDTO);
            if (operatePointUser.getUserId() != null) {
                User investUser = userService.findOne(operatePointUser.getUserId());
                UserDTO investUserDTO = new UserDTO();
                investUserDTO.setId(investUser.getId());
                investUserDTO.setPartnerMemberId(investUser.getPointUser().getPartnerMemberId());
                data.setInvestPointUser(investUserDTO);
            }
        } else {
            User investUser = userService.findOne(transfer.getUserId());
            if (investUser != null) {
                UserDTO investUserDTO = new UserDTO();
                investUserDTO.setId(investUser.getId());
                investUserDTO.setPartnerMemberId(investUser.getPointUser().getPartnerMemberId());
                data.setInvestPointUser(investUserDTO);
            }
        }

        data.setCreatedAt(transfer.getCreatedAt());
        data.setUpdatedAt(transfer.getUpdatedAt());
        return data;
    }
}
