package point.admin.controller;

import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.admin.entity.AdminUser;
import point.common.constant.ViewVariables;
import point.common.entity.BcCurrencyConfig;
import point.common.model.request.BcCurrencyConfigForm;
import point.common.model.response.CurrencyConfigData;
import point.common.model.response.PageData;
import point.common.service.BcCurrencyConfigService;
import point.common.util.JsonUtil;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/bc-currency-config")
public class V1BcCurrencyConfigRestController extends ExchangeAdminController {

    private final BcCurrencyConfigService bcCurrencyConfigService;

    @GetMapping("/page")
    @PreAuthorize("@auth.check('bc-currency-config')")
    public ResponseEntity<PageData<BcCurrencyConfig>> get(
            @RequestParam(value = "active", required = false) String active,
            @RequestParam(value = "currencyType", required = false) String currencyType,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size) {
        PageData<BcCurrencyConfig> byConditionPageData =
                bcCurrencyConfigService.findByConditionPageData(active, currencyType, number, size);
        return ResponseEntity.ok(byConditionPageData);
    }

    @GetMapping("/detail/{id}")
    public ResponseEntity<List<CurrencyConfigData>> findOne(@PathVariable Long id) {
        var bcCurrencyConfig = bcCurrencyConfigService.findOne(id);
        if (bcCurrencyConfig == null) {
            return ResponseEntity.ok().build();
        } else {
            List<CurrencyConfigData> list =
                    JsonUtil.fromJsonToList(
                            bcCurrencyConfig.getCurrencyData(), CurrencyConfigData.class);
            return ResponseEntity.ok(list);
        }
    }

    @PostMapping()
    public ResponseEntity<BcCurrencyConfig> post(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestBody BcCurrencyConfigForm currencyDataForm) {
        return ResponseEntity.ok(
                bcCurrencyConfigService.create(adminUser.getUsername(), currencyDataForm));
    }

    @DeleteMapping
    public ResponseEntity<BcCurrencyConfig> delete(@RequestParam(value = "id") Long id) {
        return ResponseEntity.ok(bcCurrencyConfigService.update(id));
    }

    @GetMapping
    public ResponseEntity<List<BcCurrencyConfig>> get() {
        return ResponseEntity.ok(bcCurrencyConfigService.findAll());
    }

    @GetMapping("/active-list")
    public ResponseEntity<List<BcCurrencyConfig>> findAllByActive() {
        return ResponseEntity.ok(bcCurrencyConfigService.findAllByActive());
    }
}
