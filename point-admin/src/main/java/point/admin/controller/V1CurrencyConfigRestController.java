package point.admin.controller;

import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.Currency;
import point.common.constant.ErrorCode;
import point.common.constant.TradeType;
import point.common.entity.CurrencyConfig;
import point.common.exception.CustomException;
import point.common.model.request.CurrencyConfigUpdateForm;
import point.common.model.response.CurrencySelData;
import point.common.service.CurrencyConfigService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/currency-config")
public class V1CurrencyConfigRestController extends ExchangeAdminController {
    private final CurrencyConfigService currencyConfigService;

    @GetMapping
    @PreAuthorize("@auth.check('currency-config')")
    public ResponseEntity<List<CurrencyConfig>> get(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "currency", required = false) Currency currency,
            @RequestParam(value = "tradeType", required = false) TradeType tradeType)
            throws Exception {
        if (id != null) {
            List<CurrencyConfig> currencyConfigs = new ArrayList<>();
            currencyConfigs.add(currencyConfigService.findOne(id));
            return ResponseEntity.ok(currencyConfigs);
        } else if (currency != null || tradeType != null) {
            List<CurrencyConfig> currencyConfigs =
                    currencyConfigService.findAllByCondition(tradeType, currency, true);
            return ResponseEntity.ok(currencyConfigs);
        } else {
            // adminは無効(enabled=false)のcurrencyも参照可能とする
            return ResponseEntity.ok(currencyConfigService.findAll());
        }
    }

    @GetMapping("/select")
    public ResponseEntity<List<CurrencySelData>> get() throws Exception {
        List<CurrencySelData> currencySelDataList = new ArrayList<>();
        currencyConfigService.findAll().stream()
                .filter(CurrencyConfig::isEnabled)
                .filter(CurrencyConfig::isVisible)
                .map(
                        currencyConfigInfo -> {
                            CurrencySelData currencySelData = new CurrencySelData();
                            currencySelData.setProperties(currencyConfigInfo);
                            return currencySelData;
                        })
                .forEach(currencySelDataList::add);

        return ResponseEntity.ok(currencySelDataList);
    }

    @PutMapping
    @PreAuthorize("@auth.check('currency-config')")
    public ResponseEntity<CurrencyConfig> update(@Valid @RequestBody CurrencyConfigUpdateForm form)
            throws Exception {

        CurrencyConfig currencyConfig = currencyConfigService.findOne(form.getId());

        if (currencyConfig == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        currencyConfig.setDepositFee(form.getDepositFee());
        currencyConfig.setWithdrawalFee(form.getWithdrawalFee());
        currencyConfig.setTransactionFee(form.getTransactionFee());
        currencyConfig.setMaxOrderAmountPerDay(form.getMaxOrderAmountPerDay());
        currencyConfig.setMinDepositAmount(form.getMinDepositAmount());
        currencyConfig.setMinWithdrawalAmount(form.getMinWithdrawalAmount());
        currencyConfig.setDepositable(form.isDepositable());
        currencyConfig.setWithdrawable(form.isWithdrawable());
        currencyConfig.setEnabled(form.isEnabled());
        return ResponseEntity.ok(currencyConfigService.save(currencyConfig));
    }

    @DeleteMapping
    public ResponseEntity<CurrencyConfig> delete(@RequestParam(value = "id") Long id)
            throws Exception {

        CurrencyConfig currencyConfig = currencyConfigService.findOne(id);

        if (currencyConfig == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        currencyConfigService.delete(currencyConfig);
        return ResponseEntity.ok(currencyConfig);
    }
}
