package point.admin.component;

import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Component;
import point.common.util.JsonUtil;

@Slf4j
@RequiredArgsConstructor
@Component
public class LogoutHandlerImpl implements LogoutHandler {

    @Override
    public void logout(
            HttpServletRequest request,
            HttpServletResponse response,
            Authentication authentication) {
        try {
            if (Objects.nonNull(authentication) && Objects.nonNull(authentication.getPrincipal())) {
                log.info(JsonUtil.encode(authentication.getPrincipal()));
            }
        } catch (Exception e) {
            if (e.getMessage()
                    .contains(
                            "Cannot invoke \"org.springframework.security.core.Authentication.getPrincipal()\"")) {
                System.out.println(e.getMessage());
            } else {
                throw e;
            }
        }
    }
}
