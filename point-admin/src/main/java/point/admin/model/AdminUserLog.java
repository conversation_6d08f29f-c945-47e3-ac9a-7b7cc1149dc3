package point.admin.model;

import java.io.IOException;
import java.io.Serializable;
import java.util.Date;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.admin.model.request.AdminUserPostForm;
import point.admin.model.request.AdminUserPutPasswordByOtherForm;
import point.admin.model.request.AdminUserPutPasswordForm;
import point.common.util.JsonUtil;

@NoArgsConstructor
public class AdminUserLog implements Serializable {

    private static final long serialVersionUID = -3841078657722021328L;

    @Getter @Setter private String adminId;
    @Getter @Setter private String activity;
    @Getter @Setter private String httpUrl;
    @Getter @Setter private String httpMethod;
    @Getter @Setter private Integer httpStatus;
    @Getter @Setter private Date createdAt;

    public AdminUserLog(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        adminId = request.getRemoteUser();
        httpUrl = request.getRequestURI();
        httpMethod = request.getMethod();
        httpStatus = response.getStatus();
        createdAt = new Date();

        if (httpMethod.toLowerCase().equals("get") || httpMethod.toLowerCase().equals("delete")) {
            activity = getActivityFromParameter(request);
        } else {
            activity = getActivityFromBody(request);
        }
    }

    private String getActivityFromBody(HttpServletRequest request) throws IOException {
        String activity = request.getReader().lines().collect(Collectors.joining(""));
        if (httpMethod.toLowerCase().equals("put")) {
            if (httpUrl.endsWith("admin/v1/admin-user/password/update-self")) {
                AdminUserPutPasswordForm form =
                        JsonUtil.decode(activity, AdminUserPutPasswordForm.class);
                form.setNewPassword("");
                form.setOldPassword("");
                activity = JsonUtil.encode(form);
            } else if (httpUrl.endsWith("admin/v1/admin-user/password/update-other")) {
                AdminUserPutPasswordByOtherForm form =
                        JsonUtil.decode(activity, AdminUserPutPasswordByOtherForm.class);
                form.setPassword("");
                activity = JsonUtil.encode(form);
            }
        } else if (httpMethod.toLowerCase().equals("post")) {
            if (httpUrl.endsWith("admin/v1/admin-user/register")) {
                AdminUserPostForm form = JsonUtil.decode(activity, AdminUserPostForm.class);
                form.setPassword("");
                activity = JsonUtil.encode(form);
            }
        }
        return activity;
    }

    private String getActivityFromParameter(HttpServletRequest request) throws IOException {
        return JsonUtil.encode(request.getParameterMap());
    }
}
