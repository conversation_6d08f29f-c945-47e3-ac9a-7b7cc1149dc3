package point.admin.model.response;

import java.math.BigDecimal;
import java.util.Date;
import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointTransferPageData {
    private Long id;
    private Long userId;
    private String partnerNumber;
    private String partnerName;
    private String partnerMemberId;
    private BigDecimal amount;
    private BigDecimal fee;
    private String status;
    private Date createdAt;
    private Date updatedAt;
}
