package point.admin.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import point.common.constant.Currency;
import point.common.constant.PosConstants;
import point.common.entity.*;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.pos.entity.PosTrade;

@Slf4j
@JsonPropertyOrder({
    "顧客ID",
    "利用者の氏名又は名称",
    "通貨",
    "自己、媒介、取次ぎ又は代理",
    "売付、買付",
    "約定年月日",
    "暗号資産の数量",
    "暗号資産の単価",
    "ステーキング申込又は報酬",
    "ステーキング申込日又は報酬日",
    "ステーキング申込又は報酬数量",
    "入金又は出金",
    "入出金日",
    "入出金額",
    "約定代金",
    "手数料",
    "差引残高",
    "注文ID",
    "約定ID",
    "入出金ID",
    "取引タイプ"
})
public record ReportCustomerLedger(
        // "顧客ID", "利用者の氏名又は名称", "通貨", "自己、媒介、取次ぎ又は代理",
        @JsonProperty("顧客ID") String userId, // 顧客ID
        @JsonProperty("利用者の氏名又は名称") String userName, // 利用者の氏名又は名称
        @JsonProperty("通貨") String currency, // 通貨
        @JsonProperty("自己、媒介、取次ぎ又は代理") String counterParty, // 自己、媒介、取次ぎ又は代理
        // "売付、買付", "約定年月日", "暗号資産の数量", "暗号資産の単価",
        @JsonProperty("売付、買付") String orderSide, // 売付、買付
        @JsonProperty("約定年月日") String tradedAt, // 約定年月日
        @JsonProperty("暗号資産の数量") String amount, // 暗号資産の数量
        @JsonProperty("暗号資産の単価") String price, // 暗号資産の単価

        // "ステーキング申込又は報酬", "ステーキング申込日又は報酬日", "ステーキング申込又は報酬数量",
        @JsonProperty("ステーキング申込又は報酬") String status, // ステーキング申込又は報酬
        @JsonProperty("ステーキング申込日又は報酬日") String applydateoramounttoaccountdate, // ステーキング申込日又は報酬日
        @JsonProperty("ステーキング申込又は報酬数量") String applyamountoramounttoaccount, // ステーキング申込又は報酬数量

        // "入金又は出金", "入出金日", "入出金額", "約定代金",
        @JsonProperty("入金又は出金") String depositWithdrawalName, // 入金又は出金
        @JsonProperty("入出金日") String depositWithdrawalDate, // 入出金日
        @JsonProperty("入出金額") String depositWithdrawalAmount, // 入出金額
        @JsonProperty("約定代金") String tradeSumPrice, // 約定代金

        // "手数料", "差引残高"
        @JsonProperty("手数料") String fee, // 手数料
        @JsonProperty("差引残高") String balance, // 差引残高
        @JsonProperty("注文ID") String orderId, // 注文ID
        @JsonProperty("約定ID") String tradeId, // 約定ID
        @JsonProperty("入出金ID") String depositWithdrawalId, // 入出金ID
        @JsonProperty("取引タイプ") String tradeType, // 取引タイプ
        @JsonIgnore String id, // 約定ID or 入出金ID
        @JsonIgnore Long userIdLong, // ユーザIDのLong値
        @JsonIgnore BigDecimal balanceBigDecimal, // balanceのBigDecimal
        @JsonIgnore Long date // 作成日時
        ) {

    // 約定Base
    public static ReportCustomerLedger createBase(
            Map<Long, Symbol> symbolMap,
            User user,
            PosTrade posTrade,
            BigDecimal balance,
            String[] list) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        if (user == null) {
            log.info(
                    "ReportError: tradeId = "
                            + posTrade.getId()
                            + " userId = "
                            + posTrade.getUserId());
            user = new User("", "");
            user.setId(0L);
            user.setTradeUncapped(true);
        }
        final var currency =
                symbolMap.get(posTrade.getSymbolId()).getCurrencyPair().getBaseCurrency();
        final var nextBalance = posTrade.nextBaseBalance(balance);
        String tradeTypeName = "";
        String tradeUncappedName = "";
        tradeUncappedName = "自己";
        return new ReportCustomerLedger(
                user.getId().toString(), // 顧客ID
                user.getName(), // 顧客の氏名
                currency.name(), // 通貨
                tradeUncappedName, // 自己、媒介、取次ぎ又は代理
                posTrade.getOrderSide().displayName, // 売付、買付
                FormatUtil.formatJst(
                        posTrade.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 約定年月日
                toStrigForReport(currency, posTrade.getAmount(), numberFormat), // 暗号資産の数量
                toStrigForReport(currency, posTrade.getPrice(), numberFormat), // 暗号資産の単価
                "", // ステーキング申込又は報酬
                "", // ステーキング申込日又は報酬日
                "", // ステーキング申込又は報酬数量
                "", // 入金又は出金
                "", // 入出金日
                "", // 入出金額
                "", // 約定代金
                "", // 手数料
                toStrigForReport(currency, nextBalance, numberFormat), // 差引残高
                posTrade.getOrderId() == null ? "" : posTrade.getOrderId().toString(), // 注文ID
                posTrade.getId() == null ? "" : posTrade.getId().toString(), // 約定ID
                "", // 入出金ID
                tradeTypeName, // 取引タイプ
                posTrade.getId().toString(), // 約定ID or 入出金ID
                user.getId(), // ユーザID
                nextBalance, // balanceのBigDecimal
                posTrade.getCreatedAt().getTime() // 作成日時
                );
    }

    // 約定Quote
    public static ReportCustomerLedger createQuote(
            Map<Long, Symbol> symbolMap,
            User user,
            PosTrade posTrade,
            BigDecimal balance,
            String[] list) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        if (user == null) {
            log.info(
                    "ReportError: tradeId = "
                            + posTrade.getId()
                            + " userId = "
                            + posTrade.getUserId());
            user = new User("", "");
            user.setId(0L);
            user.setTradeUncapped(true);
        }
        final var currency =
                symbolMap.get(posTrade.getSymbolId()).getCurrencyPair().getQuoteCurrency();
        final var nextBalance = posTrade.nextQuoteBalance(balance);
        String tradeTypeName = "";
        String tradeUncappedName = "";
        tradeTypeName = PosConstants.INVEST_TRADE_TYPE_NAME;
        tradeUncappedName = "自己";
        return new ReportCustomerLedger(
                user.getId().toString(), // 顧客ID
                user.getName(), // 顧客の氏名
                currency.name(), // 通貨
                tradeUncappedName, // 自己、媒介、取次ぎ又は代理
                posTrade.getOrderSide().getOpposite().displayName, // 売付、買付
                FormatUtil.formatJst(
                        posTrade.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 約定年月日
                "", // 暗号資産の数量
                "", // 暗号資産の単価
                "", // ステーキング申込又は報酬
                "", // ステーキング申込日又は報酬日
                "", // ステーキング申込又は報酬数量
                "", // 入金又は出金
                "", // 入出金日
                "", // 入出金額
                toStrigForReport(currency, posTrade.getAssetAmount(), numberFormat), // 約定代金
                toStrigForReport(currency, posTrade.getFee(), numberFormat), // 手数料
                toStrigForReport(currency, nextBalance, numberFormat), // 差引残高
                posTrade.getOrderId() == null ? "" : posTrade.getOrderId().toString(), // 注文ID
                posTrade.getId() == null ? "" : posTrade.getId().toString(), // 約定ID
                "", // 入出金ID
                tradeTypeName, // 取引タイプ
                posTrade.getId().toString(), // 約定ID or 入出金ID
                user.getId(), // ユーザID
                nextBalance, // balanceのBigDecimal
                posTrade.getCreatedAt().getTime() // 作成日時
                );
    }

    // 日本円入金
    public static ReportCustomerLedger create(
            User user, FiatDeposit fiatDeposit, BigDecimal balance) {
        if (user == null) {
            log.info(
                    "ReportError: fiatDepositId = "
                            + fiatDeposit.getId()
                            + " userId = "
                            + fiatDeposit.getUserId());
        }
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        final var currency = Currency.JPY;
        final var nextBalance = fiatDeposit.nextBalance(balance);
        return new ReportCustomerLedger(
                fiatDeposit.getUserId().toString(), // 顧客ID
                user.getName(), // 顧客の氏名
                currency.name(), // 通貨
                "", // 自己、媒介、取次ぎ又は代理
                "", // 売付、買付
                "", // 約定年月日
                "", // 暗号資産の数量
                "", // 暗号資産の単価
                "", // ステーキング申込又は報酬
                "", // ステーキング申込日又は報酬日
                "", // ステーキング申込又は報酬数量
                "入金", // 入金又は出金
                FormatUtil.formatJst(
                        fiatDeposit.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 入出金日
                toStrigForReport(currency, fiatDeposit.getAmount(), numberFormat), // 入出金額
                "", // 約定代金
                toStrigForReport(currency, fiatDeposit.getFee(), numberFormat), // 手数料
                toStrigForReport(currency, nextBalance, numberFormat), // 差引残高
                "", // 注文ID
                "", // 約定ID
                fiatDeposit.getId().toString(), // 入出金ID
                "", // 取引タイプ
                fiatDeposit.getId().toString(), // 約定ID or 入出金ID
                user.getId(), // ユーザID
                nextBalance, // balanceのBigDecimal
                fiatDeposit.getUpdatedAt().getTime() // 作成日時
                );
    }

    // 日本円出金
    public static ReportCustomerLedger create(
            User user, FiatWithdrawal fiatWithdrawal, BigDecimal balance) {
        if (user == null) {
            log.info(
                    "ReportError: fiatWithdrawalId = "
                            + fiatWithdrawal.getId()
                            + " userId = "
                            + fiatWithdrawal.getUserId());
        }
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        final var currency = Currency.JPY;
        final var nextBalance = fiatWithdrawal.nextBalance(balance);
        return new ReportCustomerLedger(
                fiatWithdrawal.getUserId().toString(), // 顧客ID
                user.getName(), // 顧客の氏名
                currency.name(), // 通貨
                "", // 自己、媒介、取次ぎ又は代理
                "", // 売付、買付
                "", // 約定年月日
                "", // 暗号資産の数量
                "", // 暗号資産の単価
                "", // ステーキング申込又は報酬
                "", // ステーキング申込日又は報酬日
                "", // ステーキング申込又は報酬数量
                "出金", // 入金又は出金
                FormatUtil.formatJst(
                        fiatWithdrawal.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 入出金日
                toStrigForReport(currency, fiatWithdrawal.getAmount(), numberFormat), // 入出金額
                "", // 約定代金
                toStrigForReport(currency, fiatWithdrawal.getFee(), numberFormat), // 手数料
                toStrigForReport(currency, nextBalance, numberFormat), // 差引残高
                "", // 注文ID
                "", // 約定ID
                fiatWithdrawal.getId().toString(), // 入出金ID
                "", // 取引タイプ
                fiatWithdrawal.getId().toString(), // 約定ID or 入出金ID
                user.getId(), // ユーザID
                nextBalance, // balanceのBigDecimal
                fiatWithdrawal.getUpdatedAt().getTime() // 作成日時
                );
    }

    private static String toStrigForReport(
            Currency currency, BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue =
                currency.getScaledAmount(value, RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }
}
