package point.admin.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import lombok.extern.slf4j.Slf4j;
import point.common.constant.Currency;
import point.common.constant.PosConstants;
import point.common.entity.Symbol;
import point.common.entity.User;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.pos.entity.PosOrder;
import point.pos.entity.PosTrade;

@Slf4j
@JsonPropertyOrder({
    "注文ID", "約定ID", "会員ID", "自己・媒介", "会員名", "暗号資産の名称", "売付け・買付け", "指値又は成行", "有効期限", "受注日時", "発注日時",
    "発注数", "注文数", "注文価格", "約定日時", "約定数", "約定価格", "約定金額", "キャンセル日時", "キャンセル数量", "キャンセル理由", "取引タイプ"
})
public record ReportOrderSlipStatement(
        @JsonProperty("注文ID") String orderId, // 注文ID
        @JsonProperty("約定ID") String tradeId, // 約定ID
        @JsonProperty("会員ID") String userId, // 会員ID
        @JsonProperty("自己・媒介") String isSelfTrade, // 自己・媒介
        @JsonProperty("会員名") String userName, // 会員名
        @JsonProperty("暗号資産の名称") String currencyPair, // 暗号資産の名称
        @JsonProperty("売付け・買付け") String orderSide, // 売付け・買付け
        @JsonProperty("指値又は成行") String orderType, // 指値又は成行
        @JsonProperty("有効期限") String expireDate, // 有効期限
        @JsonProperty("受注日時") String orderReceivedDate, // 受注日時
        @JsonProperty("発注日時") String orderDate, // 発注日時
        @JsonProperty("発注数") String orderingAmount, // 発注数
        @JsonProperty("注文数") String orderAmount, // 注文数
        @JsonProperty("注文価格") String orderPrice, // 注文価格
        @JsonProperty("約定日時") String tradeDate, // 約定日時
        @JsonProperty("約定数") String tradeAmount, // 約定数
        @JsonProperty("約定価格") String tradePrice, // 約定価格
        @JsonProperty("約定金額") String tradeAssetAmount, // 約定金額
        @JsonProperty("キャンセル日時") String cancelDate, // キャンセル日時
        @JsonProperty("キャンセル数量") String cancelAmount, // キャンセル数量
        @JsonProperty("キャンセル理由") String cancelReason, // キャンセル理由
        @JsonProperty("取引タイプ") String tradeType, // 取引タイプ

        // その他
        @JsonIgnore Long orderIdLong, // 注文ID
        @JsonIgnore Long userIdLong, // ユーザIDのLong値
        @JsonIgnore Long date // 作成日時
        ) {

    public static ReportOrderSlipStatement createNewOrder(
            Symbol symbol, User user, PosOrder posOrder) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        if (user == null) {
            log.error(
                    "ReportError: posOrderId = "
                            + posOrder.getId()
                            + " userId = "
                            + posOrder.getUserId());
        }
        final var currency = symbol.getCurrencyPair().getBaseCurrency();
        String tradeTypeName = "";
        String tradeUncappedName = "";
        tradeTypeName = PosConstants.INVEST_TRADE_TYPE_NAME;
        tradeUncappedName = "自己";
        return new ReportOrderSlipStatement(
                posOrder.getId().toString(), // 注文ID
                "", // 約定ID
                user.getId().toString(), // 会員ID
                tradeUncappedName, // 自己・媒介
                user.getName(), // 会員名
                symbol.getCurrencyPair().getName(), // 暗号資産の名称
                posOrder.getOrderSide().displayName, // 売付け・買付け
                posOrder.getOrderType().displayName, // 指値又は成行
                "無期限", // 有効期限
                FormatUtil.formatJst(
                        posOrder.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 受注日時
                FormatUtil.formatJst(
                        posOrder.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 発注日時
                toStrigForReport(currency, posOrder.getAmount(), numberFormat), // 発注数
                toStrigForReport(currency, posOrder.getAmount(), numberFormat), // 注文数
                toStrigForReport(currency, posOrder.getPrice(), numberFormat), // 注文価格
                "", // 約定日時
                "", // 約定数
                "", // 約定価格
                "", // 約定金額
                "", // キャンセル日時
                "", // キャンセル数量
                "", // キャンセル理由
                tradeTypeName, // 取引タイプ
                posOrder.getId(), // 注文ID
                user.getId(), // ユーザID
                posOrder.getCreatedAt().getTime() // 作成日時
                );
    }

    public static ReportOrderSlipStatement createPosTrade(
            Symbol symbol, User user, PosOrder posOrder, PosTrade posTrade) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        if (user == null) {
            log.error(
                    "ReportError: posOrderId = "
                            + posOrder.getId()
                            + " userId = "
                            + posOrder.getUserId());
        }
        final var currency = symbol.getCurrencyPair().getBaseCurrency();
        final var quoteCurrency = symbol.getCurrencyPair().getQuoteCurrency();
        String tradeTypeName = "";
        String tradeUncappedName = "";
        tradeTypeName = PosConstants.INVEST_TRADE_TYPE_NAME;
        tradeUncappedName = "自己";
        return new ReportOrderSlipStatement(
                posOrder.getId().toString(), // 注文ID
                posTrade.getId().toString(), // 約定ID
                user.getId().toString(), // 会員ID
                tradeUncappedName, // 自己・媒介
                user.getName(), // 会員名
                symbol.getCurrencyPair().getName(), // 暗号資産の名称
                posOrder.getOrderSide().displayName, // 売付け・買付け
                posOrder.getOrderType().displayName, // 指値又は成行
                "", // 有効期限
                "", // 受注日時
                "", // 発注日時
                "", // 発注数
                "", // 注文数
                "", // 注文価格
                FormatUtil.formatJst(
                        posTrade.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // 約定日時
                toStrigForReport(currency, posTrade.getAmount(), numberFormat), // 約定数
                toStrigForReportNotByCurrency(
                        symbol.getCurrencyPair().getScaledPrice(posTrade.getPrice()),
                        numberFormat), // 約定価格
                toStrigForReportNotByCurrency(
                        quoteCurrency.getScaledAmount(posTrade.getAssetAmount()),
                        numberFormat), // 約定金額
                "", // キャンセル日時
                "", // キャンセル数量
                "", // キャンセル理由
                tradeTypeName, // 取引タイプ
                posOrder.getId(), // 注文ID
                user.getId(), // ユーザID
                posTrade.getCreatedAt().getTime() // 作成日時
                );
    }

    public static ReportOrderSlipStatement createCancelOrder(
            Symbol symbol, User user, PosOrder posOrder) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        if (user == null) {
            log.error(
                    "ReportError: posOrderId = "
                            + posOrder.getId()
                            + " userId = "
                            + posOrder.getUserId());
        }
        final var currency = symbol.getCurrencyPair().getBaseCurrency();
        String tradeTypeName = "";
        String tradeUncappedName = "";
        tradeTypeName = PosConstants.INVEST_TRADE_TYPE_NAME;
        tradeUncappedName = "自己";
        return new ReportOrderSlipStatement(
                posOrder.getId().toString(), // 注文ID
                "", // 約定ID
                user.getId().toString(), // 会員ID
                tradeUncappedName, // 自己・媒介
                user.getName(), // 会員名
                symbol.getCurrencyPair().getName(), // 暗号資産の名称
                posOrder.getOrderSide().displayName, // 売付け・買付け
                posOrder.getOrderType().displayName, // 指値又は成行
                "", // 有効期限
                "", // 受注日時
                "", // 発注日時
                "", // 発注数
                "", // 注文数
                "", // 注文価格
                "", // 約定日時
                "", // 約定数
                "", // 約定価格
                "", // 約定金額
                FormatUtil.formatJst(
                        posOrder.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS), // キャンセル日時
                toStrigForReport(currency, posOrder.getRemainingAmount(), numberFormat), // キャンセル数量
                "", // キャンセル理由
                tradeTypeName, // 取引タイプ
                posOrder.getId(), // 注文ID
                user.getId(), // ユーザID
                posOrder.getUpdatedAt().getTime() // 作成日時
                );
    }

    private static String toStrigForReport(
            Currency currency, BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue =
                currency.getScaledAmount(value, RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }

    private static String toStrigForReportNotByCurrency(
            BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue = value.toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }
}
