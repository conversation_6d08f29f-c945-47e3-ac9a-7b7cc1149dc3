package point.admin.model.response;

import lombok.Getter;
import lombok.Setter;
import point.common.entity.AdminMenu;

@Getter
@Setter
public class AdminMenuData {
    private Long id;

    private String name;

    private Long pid;

    private boolean owned;

    private String group;

    public AdminMenuData(AdminMenu menu) {
        this.id = menu.getId();
        this.name = menu.getMenuName();
        this.pid = menu.getPid();
        this.group = menu.getMenuGroup();
    }
}
