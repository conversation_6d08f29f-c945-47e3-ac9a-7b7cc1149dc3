package point.admin.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

@JsonPropertyOrder({
    "ID",
    "銘柄ID",
    "ローソク足タイプ",
    "対象日時",
    "始値",
    "高値",
    "安値",
    "終値",
    "取引高",
    "確定",
    "作成日時",
    "更新日時",
    "取引タイプ"
})
public class ReportCandlestickStatement {
    @Getter
    @Setter
    @JsonProperty("ID")
    private String id;

    @Getter
    @Setter
    @JsonProperty("銘柄ID")
    private String symbolId;

    @Getter
    @Setter
    @JsonProperty("ローソク足タイプ")
    private String candlestickType;

    @Getter
    @Setter
    @JsonProperty("対象日時")
    private String targetAt;

    @Getter
    @Setter
    @JsonProperty("始値")
    private String open;

    @Getter
    @Setter
    @JsonProperty("高値")
    private String high;

    @Getter
    @Setter
    @JsonProperty("安値")
    private String low;

    @Getter
    @Setter
    @JsonProperty("終値")
    private String close;

    @Getter
    @Setter
    @JsonProperty("取引高")
    private String volume;

    @Getter
    @Setter
    @JsonProperty("確定")
    private String fixed;

    @Getter
    @Setter
    @JsonProperty("作成日時")
    private String createdAt;

    @Getter
    @Setter
    @JsonProperty("更新日時")
    private String updatedAt;

    @Getter
    @Setter
    @JsonProperty("取引タイプ")
    private String tradeType; // 取引タイプ
}
