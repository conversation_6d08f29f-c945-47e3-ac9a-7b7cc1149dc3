package point.admin.model.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import point.common.model.request.IdForm;
import point.common.model.request.UserInfoForm;

public class UserPutForm extends IdForm {

    @Getter @Setter @NotNull private String email;

    @Getter @Setter private UserInfoForm userInfo;

    @Getter @Setter @NotNull private String userStatus;

    @Getter @Setter @NotNull private String kycStatus;

    @Getter @Setter private String oldUserId;

    @Getter @Setter private Boolean insider;

    @Getter @Setter private String note;

    @Getter @Setter private Boolean insideAccountFlg;

    @JsonIgnore
    @AssertTrue(message = "名を255文字以内で入力してください。")
    public boolean isValidFirstName() {
        return userInfo.validateFirstName(false);
    }

    @JsonIgnore
    @AssertTrue(message = "姓を255文字以内で入力してください。")
    public boolean isValidLastName() {
        return userInfo.validateLastName(false);
    }

    @JsonIgnore
    @AssertTrue(message = "名（カナ）を255文字以内で入力してください。")
    public boolean isValidFirstKana() {
        return userInfo.validateFirstKana(false);
    }

    @JsonIgnore
    @AssertTrue(message = "姓（カナ）を255文字以内で入力してください。")
    public boolean isValidLastKana() {
        return userInfo.validateLastKana(false);
    }

    @JsonIgnore
    @AssertTrue(message = "国籍を255文字以内で入力してください。")
    public boolean isValidNationality() {
        if (StringUtils.isEmpty(userInfo.getNationality())) {
            return false;
        }
        return userInfo.validateNationality(false);
    }

    @JsonIgnore
    @AssertTrue(message = "都道府県を入力してください。")
    public boolean isValidPrefecture() {
        return userInfo.validatePrefecture(false);
    }

    @JsonIgnore
    @AssertTrue(message = "市区町村を255文字以内で入力してください。")
    public boolean isValidCity() {
        return userInfo.validateCity(false);
    }

    @JsonIgnore
    @AssertTrue(message = "町名番地を255文字以内で入力してください。")
    public boolean isValidAddress() {
        return userInfo.validateAddress(false);
    }

    @JsonIgnore
    @AssertTrue(message = "建物名を255文字以内で入力してください。")
    public boolean isValidBuilding() {
        return userInfo.validateBuilding(true);
    }

    @JsonIgnore
    @AssertTrue(message = "電話番号は10桁か11桁で、1文字目は0を入力してください。")
    public boolean isValidPhoneNumber() {
        return userInfo.validatePhoneNumber(false);
    }

    @JsonIgnore
    @AssertTrue(message = "勤務先を255文字以内で入力してください。")
    public boolean isValidWorkPlace() {
        // 職業 専業主婦 / 主夫・学生・無職が選ばれた場合、勤務地は空を許可
        if (userInfo.canEmptyOccupation(false)) {
            return true;
        }

        return userInfo.validateWorkPlace(false);
    }

    @JsonIgnore
    @AssertTrue(message = "部署・役職を255文字以内で入力してください。")
    public boolean isValidPosition() {
        // 職業 専業主婦 / 主夫・学生・無職が選ばれた場合、部署・役職は空を許可
        if (userInfo.canEmptyOccupation(false)) {
            return true;
        }

        return userInfo.validatePosition(false);
    }

    @JsonIgnore
    @AssertTrue(message = "職業を選択してください。")
    public boolean isValidOccupation() {
        return userInfo.validateOccupation(false);
    }

    @JsonIgnore
    @AssertTrue(message = "業種を選択してください。")
    public boolean isValidIndustry() {
        // 職業 会社役員 / 団体役員・会社員 / 団体職員・個人事業主 / 自営業が選ばれた時に業種を空にすることはできませんか判定
        if (userInfo.notEmptyOccupation(false)) {
            return true;
        }
        // requirement for CBOA  data migration ,industry can be null
        return userInfo.validateBirthday(true);
    }

    @JsonIgnore
    @AssertTrue(message = "生年月日を8桁の数値で入力してください。")
    public boolean isValidBirthday() {
        return userInfo.validateBirthday(false);
    }

    @JsonIgnore
    @AssertTrue(message = "性別を選択してください。")
    public boolean isValidGender() {
        return userInfo.validateGender(false);
    }

    @JsonIgnore
    @AssertTrue(message = "郵便番号を7桁の数値で入力してください。")
    public boolean isValidZipCode() {
        return userInfo.validateZipCode(false);
    }

    @JsonIgnore
    @AssertTrue(message = "年収を選択してください。")
    public boolean isValidIncome() {
        return userInfo.validateIncome(false);
    }

    @JsonIgnore
    @AssertTrue(message = "金融資産を選択してください。")
    public boolean isValidFinancialAssets() {
        return userInfo.validateFinancialAssets(false);
    }

    @JsonIgnore
    @AssertTrue(message = "主なご利用目的を選択してください。")
    public boolean isValidPurpose() {
        return userInfo.validatePurpose(false);
    }

    @JsonIgnore
    @AssertTrue(message = "投資目的を選択してください。")
    public boolean isValidInvestmentPurposes() {
        return userInfo.validateInvestmentPurposes(false);
    }

    @JsonIgnore
    @AssertTrue(message = "投資経験（暗号資産）を選択してください。")
    public boolean isValidCryptoExperience() {
        return userInfo.validateCryptoExperience(false);
    }

    @JsonIgnore
    @AssertTrue(message = "投資経験（FX）を選択してください。")
    public boolean isValidFxExperience() {
        return userInfo.validateFxExperience(false);
    }

    @JsonIgnore
    @AssertTrue(message = "投資経験（株式投資）を選択してください。")
    public boolean isValidStocksExperience() {
        return userInfo.validateStocksExperience(false);
    }

    @JsonIgnore
    @AssertTrue(message = "投資経験（投資信託）を選択してください。")
    public boolean isValidFundExperience() {
        return userInfo.validateFundExperience(false);
    }

    @JsonIgnore
    @AssertTrue(message = "申込経緯を選択してください。")
    public boolean isValidApplicationHistory() {
        return userInfo.validateApplicationHistory(false);
    }

    @JsonIgnore
    @AssertTrue(message = "申込経緯(その他)を入力してください。")
    public boolean isValidApplicationHistoryOther() {
        // 申込経緯（その他）が選択されている場合のみチェック
        if (userInfo.IsApplicationHistoryOther(false)) {
            return userInfo.validateApplicationHistoryOther(false);
        }
        return true;
    }

    @JsonIgnore
    @AssertTrue(message = "外国PEPsを選択してください。")
    public boolean isValidForeignPeps() {
        return userInfo.validateForeignPeps(false);
    }

    @JsonIgnore
    @AssertTrue(message = "取引所会員IDを数値で入力してください。")
    public boolean isValidOldUserId() {
        return NumberUtils.isParsable(oldUserId) || StringUtils.isEmpty(oldUserId);
    }

    @JsonIgnore
    @AssertTrue(message = "国番号を255文字以内で入力してください。")
    public boolean isValidCountry() {
        return userInfo.validateCountry(true);
    }
}
