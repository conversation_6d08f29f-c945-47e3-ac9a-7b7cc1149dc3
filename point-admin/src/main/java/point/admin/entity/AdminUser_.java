package point.admin.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.entity.AbstractEntity_;
import point.common.entity.AdminRole;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(AdminUser.class)
public abstract class AdminUser_ extends AbstractEntity_ {

    public static volatile SingularAttribute<AdminUser, String> password;
    public static volatile SingularAttribute<AdminUser, Boolean> credentialsNonExpired;
    public static volatile SingularAttribute<AdminUser, Boolean> accountNonExpired;
    public static volatile SingularAttribute<AdminUser, String> email;
    public static volatile SingularAttribute<AdminUser, Boolean> enabled;
    public static volatile SingularAttribute<AdminUser, Boolean> passwordForceChange;
    public static volatile ListAttribute<AdminUser, AdminUserAuthority> authorities;
    public static volatile SingularAttribute<AdminUser, Boolean> accountNonLocked;
    public static volatile SingularAttribute<AdminUser, AdminRole> role;

    public static final String PASSWORD = "password";
    public static final String CREDENTIALS_NON_EXPIRED = "credentialsNonExpired";
    public static final String ACCOUNT_NON_EXPIRED = "accountNonExpired";
    public static final String EMAIL = "email";
    public static final String ENABLED = "enabled";
    public static final String PASSWORD_FORCE_CHANGE = "passwordForceChange";
    public static final String AUTHORITIES = "authorities";
    public static final String ACCOUNT_NON_LOCKED = "accountNonLocked";
}
