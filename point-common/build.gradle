plugins {
    id 'io.freefair.lombok' version '6.3.0'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'org.springframework.boot' version '2.6.1'
}


  version = '0.0.1-SNAPSHOT'
  sourceCompatibility = '17'
  targetCompatibility = '17'


dependencies {

  implementation 'com.auth0:java-jwt:3.18.2'
  implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-csv'
  implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-xml'
  implementation 'com.ibm.icu:icu4j:70.1'
  implementation 'commons-io:commons-io:2.11.0'
  implementation 'net.logstash.logback:logstash-logback-encoder:6.6'
  implementation 'org.springframework.boot:spring-boot-configuration-processor'
  implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
  implementation 'org.springframework.boot:spring-boot-starter-data-redis'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-security'
  implementation 'org.springframework.boot:spring-boot-starter-jersey'

  implementation platform('com.amazonaws:aws-java-sdk-bom:1.12.128')
  implementation 'com.amazonaws:aws-java-sdk-s3'

  implementation 'com.amazonaws:aws-java-sdk-sns'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
  implementation 'org.springframework.boot:spring-boot-starter-mail'
  implementation 'org.springframework.session:spring-session-data-redis'
  implementation "com.github.spotbugs:spotbugs:4.7.3"

  implementation 'org.tomitribe:tomitribe-http-signatures:1.3'
  implementation 'javax.ws.rs:javax.ws.rs-api:2.1.1'
  implementation 'io.gsonfire:gson-fire:1.8.0'
  implementation 'com.squareup.okhttp:okhttp:2.7.5'
  implementation 'com.squareup.okhttp:logging-interceptor:2.7.5'
  implementation 'io.springfox:springfox-swagger2:2.7.0'
  implementation 'io.springfox:springfox-swagger-ui:2.7.0'
  implementation 'com.itextpdf:itextpdf:5.5.6'
  implementation 'com.itextpdf:itext-asian:5.2.0'

  implementation 'org.apache.commons:commons-collections4:4.4'
  implementation 'org.jetbrains:annotations:26.0.2'


  testImplementation 'org.mockito:mockito-inline'
  testImplementation 'org.mockito:mockito-junit-jupiter'
  testImplementation 'org.junit.jupiter:junit-jupiter-engine'
  testImplementation 'org.junit.jupiter:junit-jupiter-api'
}

dependencyManagement {
  imports {
    mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
  }
}


bootJar {
    enabled = false
}
jar {
    enabled = true
}

bootBuildImage {
    enabled = false
}

test {
  useJUnitPlatform()
}
