package point.common.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

@Slf4j
public enum DateUnit {
    SECOND(1000),
    MINUTE(1000 * 60),
    HOUR(1000 * 60 * 60),
    DAY(1000 * 60 * 60 * 24);

    private static final String JP_DATE_FORMAT_AM_PM = "yyyy年MM月dd日HH:mm:ss";
    private static final String SLASH_YYYY_MM_DD_HH_MM_SS = "yyyy/MM/dd HH:mm:ss";
    private static final String SLASH_YYYY_MM_DD = "yyyy/MM/dd";
    private static final String JAPAN = "Japan";
    private static final String SLASH_YYYYMMDD = "yyyyMMdd";
    private static final DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern(SLASH_YYYYMMDD);
    private static final DateTimeFormatter HHMMSS = DateTimeFormatter.ofPattern("HHmmss");
    public static final ZoneId ZONE_ID_TOKYO = ZoneId.of("Asia/Tokyo");
    public static final ZoneId ZONE_ID_UTC = ZoneId.of("UTC");
    @Getter private final long millis;

    DateUnit(long millis) {
        this.millis = millis;
    }

    public Date truncate(long millis) {
        return new Date(millis - millis % this.millis);
    }

    public static String getYYMMDDString(LocalDate localDate) {
        ZonedDateTime jstDate =
                localDate.atStartOfDay(ZoneId.systemDefault()).withZoneSameInstant(ZONE_ID_TOKYO);
        return jstDate.format(YYYYMMDD);
    }

    public static String getDateString(LocalDate localDate, String formatter) {
        return localDate.format(DateTimeFormatter.ofPattern(formatter));
    }

    public static String getHHMMSSString(LocalDateTime localDateTime) {
        ZonedDateTime jstDateTime =
                localDateTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(ZONE_ID_TOKYO);
        return jstDateTime.format(HHMMSS);
    }

    public static Date getTodayEndDate(Date date) {
        var zoneId = ZoneId.of("Asia/Tokyo");
        var localDate = date.toInstant().atZone(zoneId).toLocalDateTime();
        var localEndDate = getEndDate(localDate);
        var res = Date.from(localEndDate.atZone(zoneId).toInstant());
        return res;
    }

    public static Date getTommorowStartDate(Date date) {
        var zoneId = ZoneId.of("Asia/Tokyo");
        var localDate = date.toInstant().atZone(zoneId).toLocalDateTime().plusDays(1);
        var localEndDate = getStartDate(localDate);
        var res = Date.from(localEndDate.atZone(zoneId).toInstant());
        return res;
    }

    public static LocalDateTime getEndDate(LocalDateTime date) {
        return LocalDateTime.of(date.toLocalDate(), LocalTime.MAX);
    }

    public static LocalDateTime getStartDate(LocalDateTime date) {
        return LocalDateTime.of(date.toLocalDate(), LocalTime.MIN);
    }

    public static String formatJapanDate(Long epochMilli) {
        Instant instant = Instant.ofEpochMilli(epochMilli);
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneId.of(JAPAN));
        return zonedDateTime.format(DateTimeFormatter.ofPattern(JP_DATE_FORMAT_AM_PM));
    }

    // FromTo日付を作成しPair(from, to)で返却する
    // date = 2022/02/02 12:12:12 のときに from = 2022/02/02 00:00:00, to = 2022/02/03 00:00:00 となる
    public static Pair<Date, Date> createFromTo(Long date) {
        final var dateFrom =
                Instant.ofEpochMilli(date)
                        .atZone(ZoneId.of("Asia/Tokyo"))
                        .truncatedTo(ChronoUnit.DAYS);
        final var dateTo = dateFrom.plusDays(1);
        return Pair.of(Date.from(dateFrom.toInstant()), Date.from(dateTo.toInstant()));
    }

    public static Date toYesterday(Date date) {
        final var zdt = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of("Asia/Tokyo"));
        return Date.from(zdt.minusDays(1).toInstant());
    }

    public static String toFormatSlashDate(Date date) {
        final var zdt = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of("Asia/Tokyo"));
        return zdt.format(DateTimeFormatter.ofPattern(SLASH_YYYY_MM_DD));
    }

    public static String toFormatSlashDateTime(Date date) {
        final var zdt = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of("Asia/Tokyo"));
        return zdt.format(DateTimeFormatter.ofPattern(SLASH_YYYY_MM_DD_HH_MM_SS));
    }

    public static Date addDate(Date date, int field, int amount) {
        Calendar c = Calendar.getInstance();
        if (date != null) {
            c.setTime(date);
        }

        c.add(field, amount);
        return c.getTime();
    }

    public static Date toFormatStrToDate(String source) {
        LocalDate localDate = LocalDate.parse(source, YYYYMMDD);
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZONE_ID_TOKYO);
        return Date.from(zonedDateTime.toInstant());
    }

    public static DateRange getCurrentDateRange() {
        // 現在の日付を取得します。
        LocalDate currentDate = LocalDate.now();

        LocalDate previousDate = currentDate.minusDays(1);
        // 当日の開始時間の文字列を構築します。
        LocalDateTime localDateTimeStart = previousDate.atStartOfDay();
        // 当日の終了時間の文字列を構築します。
        LocalDateTime localDateTimeEnd = currentDate.atTime(23, 59, 59);
        // 日付と時間のフォーマッターを定義します。
        // システムのデフォルトタイムゾーンを取得します。
        ZoneId zoneId = ZoneId.systemDefault();
        // 開始時間の LocalDateTime オブジェクトを Date オブジェクトに変換します。
        Date startDate = Date.from(localDateTimeStart.atZone(zoneId).toInstant());
        // 終了時間の LocalDateTime オブジェクトを Date オブジェクトに変換します。
        Date endDate = Date.from(localDateTimeEnd.atZone(zoneId).toInstant());
        // DateRange オブジェクトを作成して返します。
        return new DateRange(startDate, endDate);
    }

    public static LocalDate getCurrentDateJST() {
        ZoneId jstZone = ZoneId.of("Asia/Tokyo");
        ZonedDateTime nowUTC = ZonedDateTime.now(ZoneOffset.UTC);
        ZonedDateTime nowJST = nowUTC.withZoneSameInstant(jstZone);
        LocalDate currentDateJST = nowJST.toLocalDate();
        log.info("当日JST時間: {}", currentDateJST);
        return currentDateJST;
    }

    public static LocalDateTime convertToLocalDateTimeWithZoneId(Date date, ZoneId zoneId) {
        return date.toInstant().atZone(zoneId).toLocalDateTime();
    }

    public static LocalDateTime getLocalDateTimeWithZoneId(ZoneId zoneId) {
        return LocalDateTime.now(zoneId);
    }

    public static TimeRange getJstDayUtcRange(LocalDate date) {
        // JST 00:00:00
        ZonedDateTime jstStart = date.atStartOfDay(ZONE_ID_TOKYO);
        // JST 23:59:59.999999999
        ZonedDateTime jstEnd = date.plusDays(1).atStartOfDay(ZONE_ID_TOKYO).minusNanos(1);

        // convert to UTC
        Instant utcStart = jstStart.toInstant();
        Instant utcEnd = jstEnd.toInstant();

        return new TimeRange(utcStart, utcEnd);
    }
}
