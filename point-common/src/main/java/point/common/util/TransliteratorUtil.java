package point.common.util;

import com.ibm.icu.text.Transliterator;

public class TransliteratorUtil {

    private static final Transliterator FULL_WIDTH_TO_HALF_WIDTH =
            Transliterator.getInstance("Fullwidth-Halfwidth");

    private static final Transliterator HALF_WIDTH_TO_FULL_WIDTH =
            Transliterator.getInstance("Halfwidth-Fullwidth");

    public static String fullWidthToHalfWidth(String source) {
        return FULL_WIDTH_TO_HALF_WIDTH.transliterate(source);
    }

    public static String halfWidthToFullWidth(String source) {
        return HALF_WIDTH_TO_FULL_WIDTH.transliterate(source);
    }
}
