package point.common.util;

import java.util.Map;
import point.common.constant.Currency;
import point.common.constant.CurrencyPair;
import point.common.constant.PosConstants;
import point.common.model.response.ExchangeSummaryReportData;
import point.common.model.response.ReportData;

public final class CurrencyUtils {

    public static String getCurrencyName(Currency currency) {
        if (Currency.JPY.equals(currency)) {
            return PosConstants.CURRENCY_JYP;
        } else if (Currency.BALC.equals(currency)) {
            return PosConstants.CURRENCY_BALC;
        } else if (Currency.ACTC.equals(currency)) {
            return PosConstants.CURRENCY_ACTC;
        } else {
            return currency.getName();
        }
    }

    public static String getCurrencyPairName(CurrencyPair currencyPair) {
        if (CurrencyPair.BALC_JPY.equals(currencyPair)) {
            return PosConstants.getCurrencyBalcName();
        } else if (CurrencyPair.ACTC_JPY.equals(currencyPair)) {
            return PosConstants.getCurrencyActcName();
        } else {
            return PosConstants.getCurrencyName(currencyPair);
        }
    }

    public static void putCurrencyToMap(Map<String, String> map, Currency currency) {
        map.put("currency", getCurrencyName(currency));
    }

    public static void setCurrencyToReport(ExchangeSummaryReportData report, Currency currency) {
        report.setCurrency(getCurrencyName(currency));
    }

    public static void setCurrencyToAssetReport(
            ReportData.AssetReport assetReport, Currency currency) {
        assetReport.setCurrency(getCurrencyName(currency));
    }

    public static void setSymbolToPosTradeReport(
            ReportData.PosTradeReport posTradeReport, CurrencyPair currencyPair) {
        posTradeReport.setCurrencyPair(getCurrencyPairName(currencyPair));
    }

    public static void setCurrencyToEntity(CurrencySetter entity, Currency currency) {
        entity.setCurrency(getCurrencyName(currency));
    }

    public static void setCurrencyPairToEntity(
            CurrencyPairSetter entity, CurrencyPair currencyPair) {
        if (CurrencyPair.BALC_JPY.equals(currencyPair)) {
            entity.setCurrencyPair(PosConstants.CURRENCY_BALC_JPY);
        } else if (CurrencyPair.ACTC_JPY.equals(currencyPair)) {
            entity.setCurrencyPair(PosConstants.CURRENCY_ACTC_JPY);
        } else {
            entity.setCurrencyPair(
                    currencyPair.getName().replace(PosConstants.JPY, PosConstants.CURRENCY_JYP));
        }
    }

    @FunctionalInterface
    public interface CurrencySetter {
        void setCurrency(String currency);
    }

    @FunctionalInterface
    public interface CurrencyPairSetter {
        void setCurrencyPair(String currencyPair);
    }
}
