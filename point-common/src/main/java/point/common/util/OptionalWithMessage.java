package point.common.util;

import java.util.Optional;
import lombok.Getter;

@Getter
public class OptionalWithMessage<T> {
    private final T value;
    private final String message;

    public OptionalWithMessage(T value, String message) {
        this.value = value;
        this.message = message;
    }

    public static <T> OptionalWithMessage<T> of(T value) {
        return new OptionalWithMessage<>(value, null);
    }

    public static <T> OptionalWithMessage<T> empty(String message) {
        return new OptionalWithMessage<>(null, message);
    }

    public Optional<T> toOptional() {
        return Optional.ofNullable(value);
    }

    public boolean isPresent() {
        return this.toOptional().isPresent();
    }
}
