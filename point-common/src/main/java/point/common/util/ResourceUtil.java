package point.common.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import org.springframework.core.io.ClassPathResource;

public class ResourceUtil {

    public static String load(String filePath) throws IOException {
        File resource = new ClassPathResource(filePath).getFile();
        return new String(Files.readAllBytes(resource.toPath()));
    }

    public static String load(Object obj, String filePath) throws IOException {
        InputStream inputStream = obj.getClass().getResourceAsStream(filePath);
        String result = "";
        String line = "";

        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
        while ((line = br.readLine()) != null) {
            result += line + "\r\n";
        }
        br.close();
        return result;
    }
}
