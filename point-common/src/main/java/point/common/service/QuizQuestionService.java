package point.common.service;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import point.common.component.CustomTransactionManager;
import point.common.constant.*;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.model.request.QuizQuestionForm;
import point.common.model.response.*;
import point.common.repos.QuizQuestionPublishedRecordRepository;
import point.common.repos.QuizQuestionRepository;
import point.common.repos.QuizUserAnswerRepository;

@Slf4j
@Service
@RequiredArgsConstructor
public class QuizQuestionService {
    public final QuizQuestionPublishedRecordRepository quizQuestionPublishedRecordRepository;
    public final QuizQuestionRepository quizQuestionRepository;
    public final ChoiceActivityRuleService choiceActivityRuleService;
    public final ChoicePowerSyncService choicePowerSyncService;
    public final QuizUserAnswerRepository quizUserAnswerRepository;
    private final CustomTransactionManager customTransactionManager;
    private final QuizUserAnswerService quizUserAnswerService;

    public ResponseEntity<GlobalApiResponse<QuizQuestionData>> getQuiz(List<Long> userIds) {
        log.info("userIds:{},get quiz question", userIds);
        Date date = getNowDate();

        List<QuizUserAnswer> quizUserAnswers =
                quizUserAnswerRepository.findByAnswerDateAndUserIdIn(date, userIds);

        QuizQuestionPublishedRecord publishedRecord = getQuizRecord(date);
        if (publishedRecord == null) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    400,
                                    ErrorCode.REQUEST_ERROR_QUIZ_NOT_IN_VALID_DATE_RANGE
                                            .getMessage()));
        }

        // already do this quiz
        if (CollectionUtils.isNotEmpty(quizUserAnswers)) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    200,
                                    QuizQuestionData.convertToRandomizedDto(
                                            publishedRecord,
                                            quizUserAnswers.get(0).getUserAnswer())));
        }
        return ResponseEntity.ok()
                .body(
                        new GlobalApiResponse<>(
                                200, QuizQuestionData.convertToRandomizedDto(publishedRecord)));
    }

    private QuizQuestionPublishedRecord getQuizRecord(Date date) {
        return quizQuestionPublishedRecordRepository.findByPublishedDate(date);
    }

    private Date getNowDate() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime truncated = now.withMinute(0).withSecond(0).withNano(0);
        return Date.from(truncated.atZone(ZoneId.systemDefault()).toInstant());
    }

    public ResponseEntity<GlobalApiResponse<String>> submitQuestion(
            List<Long> userIds, UserIdType userType, Long questionId, String answer)
            throws Exception {

        Optional<QuizQuestion> opt = quizQuestionRepository.findById(questionId);
        if (opt.isEmpty()) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    400, ErrorCode.REQUEST_ERROR_QUIZ_NOT_EXIST.getMessage()));
        }
        if (StringUtils.isBlank(answer)) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    400, ErrorCode.REQUEST_ERROR_QUIZ_NOT_EXIST.getMessage()));
        }

        QuizQuestion quizQuestion = opt.get();

        Date now = getNowDate();

        QuizQuestionPublishedRecord quizQuestionPublishedRecord = getQuizRecord(now);

        if (!quizQuestion.getId().equals(quizQuestionPublishedRecord.getQuizQuestion().getId())) {
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    400,
                                    ErrorCode.REQUEST_ERROR_QUIZ_QUESTION_NOT_OPEN.getMessage()));
        }

        List<QuizUserAnswer> quizUserAnswers =
                quizUserAnswerRepository.findByAnswerDateAndUserIdInAndQuizId(
                        now, userIds, questionId);

        if (CollectionUtils.isNotEmpty(quizUserAnswers)) {
            log.info(
                    "user type:{}, userIds:{},already do this quiz:{}",
                    userType,
                    userIds,
                    questionId);
            return ResponseEntity.ok()
                    .body(
                            new GlobalApiResponse<>(
                                    400,
                                    ErrorCode.REQUEST_ERROR_QUIZ_QUESTION_ALREADY_ANSWER
                                            .getMessage()));
        }
        log.info("user type:{},userIds:{},start do quiz:{}", userType, userIds, questionId);
        Long userId = userIds.get(0);
        if (Objects.isNull(userId)) {
            return ResponseEntity.ok()
                    .body(GlobalApiResponse.badRequest(ErrorCode.REQUEST_ERROR_USER_ID_NOT_FOUND));
        }

        return customTransactionManager.execute(
                entityManager -> {
                    if (quizQuestionPublishedRecord.getRandomAnswer().equals(answer)) {

                        ChoiceActivityRuleEnum activityRuleEnum = ChoiceActivityRuleEnum.PASS_QUIZ;

                        ChoiceActivityRule choiceActivityRule =
                                choiceActivityRuleService.findOneByEffectiveDate(
                                        activityRuleEnum.getId());

                        Long powerAmount = choiceActivityRule.getPowerAmount();

                        ChoicePowerSync choicePowerSync =
                                ChoicePowerSync.builder()
                                        .userId(userId)
                                        .choiceActivityRuleId(choiceActivityRule.getId())
                                        .activityFunction(choiceActivityRule.getActivityFunction())
                                        .obtainFrequency(choiceActivityRule.getObtainFrequency())
                                        .getType(choiceActivityRule.getGetType())
                                        .synchronizeFlag(0)
                                        .powerAmount(powerAmount)
                                        .build();
                        choicePowerSyncService.save(choicePowerSync, entityManager);

                        saveQuizUserAnswer(quizQuestion, quizQuestionPublishedRecord, now, userId, answer, entityManager);
                        log.info(
                                "user type:{},user id:{}, quiz id:{},choice the correct answer:{}",
                                userType.name(),
                                userId,
                                quizQuestion.getId(),
                                answer);
                        return ResponseEntity.ok()
                                .body(
                                        new GlobalApiResponse<>(
                                                200, quizQuestion.getCorrectAnswerContent()));
                    } else {
                        saveQuizUserAnswer(quizQuestion, quizQuestionPublishedRecord, now, userId, answer, entityManager);
                        log.info(
                                "user type:{},user id:{}, quiz id:{},choice the incorrect answer:{}",
                                userType.name(),
                                userId,
                                quizQuestion.getId(),
                                answer);
                        return ResponseEntity.ok()
                                .body(
                                        new GlobalApiResponse<>(
                                                400,
                                                ErrorCode.REQUEST_ERROR_QUIZ_QUESTION_WRONG_ANSWER
                                                        .getMessage()));
                    }
                });
    }

    public void saveQuizUserAnswer(
            QuizQuestion quizQuestion,
            QuizQuestionPublishedRecord record,
            Date now,
            Long userId,
            String answer,
            EntityManager entityManager)
            throws Exception {
        QuizUserAnswer currentUserAnswer =
                QuizUserAnswer.builder()
                        .quizId(quizQuestion.getId())
                        .quizNum(quizQuestion.getQuizNum())
                        .quizTitle(quizQuestion.getTitle())
                        .userAnswer(answer)
                        .answerDate(now)
                        .userId(userId)
                        .correctAnswer(record.getRandomAnswer())
                        .originalAnswer(quizQuestion.getAnswer())
                        .publishedRecordId(record.getId())
                        .build();
        quizUserAnswerService.save(currentUserAnswer, entityManager);
    }

    public void randomQuizQuestionDaily() {

        List<QuizQuestion> list =
                quizQuestionRepository.findAll(PageRequest.of(0, 5000)).getContent();

        List<Long> ids = list.stream().map(QuizQuestion::getId).collect(Collectors.toList());

        long seed = System.currentTimeMillis();
        Random random = new Random(seed);

        Collections.shuffle(ids, random);

        List<Long> selectedIds = ids.subList(0, 24);

        List<QuizQuestion> resultList =
                list.stream().filter(question -> selectedIds.contains(question.getId())).toList();

        List<Date> dateList = initTomorrow24Hours();

        List<QuizQuestionPublishedRecord> result = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            List<String> options = CommonConstants.OPTIONS;
            String randomAnswer = options.get(new Random().nextInt(options.size()));

            result.add(
                    QuizQuestionPublishedRecord.builder()
                            .quizQuestion(resultList.get(i))
                            .quizQuestionId(resultList.get(i).getId())
                            .publishedDate(dateList.get(i))
                            .randomAnswer(randomAnswer)
                            .answerMapping(
                                    buildSwapOption(randomAnswer, resultList.get(i).getAnswer()))
                            .build());
        }
        quizQuestionPublishedRecordRepository.saveAll(result);
    }

    private String buildSwapOption(String randomAnswer, String originalAnswer) {
        Map<String, String> answerMap = new HashMap<>();
        answerMap.put(originalAnswer, randomAnswer);
        if (!originalAnswer.equals(randomAnswer)) {
            answerMap.put(randomAnswer, originalAnswer);
        }
        return answerMap.entrySet().stream()
                .map(entry -> entry.getKey() + CommonConstants.COLON + entry.getValue())
                .distinct()
                .collect(Collectors.joining(CommonConstants.COMMA));
    }

    private List<Date> initTomorrow24Hours() {

        List<Date> timeList = new ArrayList<>();

        ZoneId jstZone = ZoneId.of("Asia/Tokyo");

        ZoneId utcZone = ZoneId.of("UTC");

        LocalDate date = LocalDate.now().plusDays(1);

        // 遍历一天的 24 个小时
        for (int hour = 0; hour < 24; hour++) {
            LocalDateTime jstDateTime = LocalDateTime.of(date, LocalTime.of(hour, 0));
            ZonedDateTime jstZonedDateTime = jstDateTime.atZone(jstZone);
            ZonedDateTime utcZonedDateTime = jstZonedDateTime.withZoneSameInstant(utcZone);

            Date utcDate = Date.from(utcZonedDateTime.toInstant());
            timeList.add(utcDate);
        }
        return timeList;
    }

    public PageData<QuizQuestion> findByPage(
            Integer size,
            Integer number,
            String quizNum,
            String title,
            Long createdAtFrom,
            Long createdAtTo) {
        Pageable pageable = PageRequest.of(number, size, Sort.by(Sort.Direction.ASC, "id"));

        Page<QuizQuestion> page =
                quizQuestionRepository.findAll(
                        (root, criteriaQuery, criteriaBuilder) -> {
                            List<Predicate> list = new ArrayList<>();

                            if (StringUtils.isNotEmpty(quizNum)) {
                                list.add(criteriaBuilder.like(root.get("quizNum"), quizNum + "%"));
                            }

                            if (StringUtils.isNotEmpty(title)) {
                                list.add(criteriaBuilder.like(root.get("title"), title + "%"));
                            }

                            if (createdAtFrom != null) {
                                list.add(
                                        criteriaBuilder.greaterThanOrEqualTo(
                                                root.get("createdAt"), new Date(createdAtFrom)));
                            }

                            if (createdAtTo != null) {
                                list.add(
                                        criteriaBuilder.lessThanOrEqualTo(
                                                root.get("createdAt"), new Date(createdAtTo)));
                            }
                            return criteriaBuilder.and(list.toArray(new Predicate[0]));
                        },
                        pageable);

        return new PageData<>(number, size, page.getTotalElements(), page.getContent());
    }

    public void saveQuiz(String email, QuizQuestionForm quizQuestionForm) throws CustomException {

        QuizQuestion question = quizQuestionForm.initQuizQuestion();
        if (quizQuestionRepository.existsByQuizNum(question.getQuizNum())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_DUPLICATE_QUIZ_NUM);
        }
        if (quizQuestionRepository.existsByTitle(question.getTitle())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_DUPLICATE_QUIZ_TITLE);
        }
        question.setCreatedUser(email.split("@")[0]);
        quizQuestionRepository.save(question);
    }

    public void editQuiz(String email, QuizQuestionForm quizQuestionForm) throws CustomException {

        QuizQuestion question = quizQuestionForm.initExistQuizQuestion();

        Optional<QuizQuestion> opt = quizQuestionRepository.findById(question.getId());
        if (opt.isEmpty()) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_QUIZ_NOT_EXIST);
        }
        if (!question.getQuizNum().equals(opt.get().getQuizNum())) {
            if (quizQuestionRepository.existsByQuizNum(question.getQuizNum())) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_DUPLICATE_QUIZ_NUM);
            }
        }
        if (!question.getTitle().equals(opt.get().getTitle())) {
            if (quizQuestionRepository.existsByTitle(question.getTitle())) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_DUPLICATE_QUIZ_TITLE);
            }
        }
        question.setUpdatedUser(email.split("@")[0]);
        question.setCreatedUser(email.split("@")[0]);
        question.setCreatedAt(opt.get().getCreatedAt());
        quizQuestionRepository.save(question);
    }

    public void enableSwitch(Long quizId, boolean enable) throws CustomException {

        Optional<QuizQuestion> opt = quizQuestionRepository.findById(quizId);

        if (opt.isEmpty()) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_QUIZ_NOT_EXIST);
        }
        QuizQuestion quiz = opt.get();
        quiz.setEnable(enable);
        quizQuestionRepository.save(quiz);
    }

    public QuizQuestion getQuestion(Long id) throws CustomException {
        Optional<QuizQuestion> opt = quizQuestionRepository.findById(id);
        if (opt.isEmpty()) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_QUIZ_NOT_EXIST);
        }
        return opt.get();
    }

    public PageData<QuizQuestionPublishedRecordData> findPublishedRecordByPage(
            Integer size,
            Integer number,
            String quizNum,
            String title,
            Long publishedFrom,
            Long publishedTo) {
        Pageable pageable = PageRequest.of(number, size, Sort.by(Sort.Direction.DESC, "id"));

        Page<QuizQuestionPublishedRecord> page =
                quizQuestionPublishedRecordRepository.findAll(
                        (root, criteriaQuery, criteriaBuilder) -> {
                            List<Predicate> list = new ArrayList<>();

                            if (StringUtils.isNotEmpty(quizNum)) {
                                list.add(
                                        criteriaBuilder.like(
                                                root.get("quizQuestion").get("quizNum"),
                                                quizNum + "%"));
                            }

                            if (StringUtils.isNotEmpty(title)) {
                                list.add(
                                        criteriaBuilder.like(
                                                root.get("quizQuestion").get("title"),
                                                title + "%"));
                            }

                            if (publishedFrom != null) {
                                list.add(
                                        criteriaBuilder.greaterThanOrEqualTo(
                                                root.get("publishedDate"),
                                                new Date(publishedFrom)));
                            }

                            if (publishedTo != null) {
                                list.add(
                                        criteriaBuilder.lessThanOrEqualTo(
                                                root.get("publishedDate"), new Date(publishedTo)));
                            }
                            return criteriaBuilder.and(list.toArray(new Predicate[0]));
                        },
                        pageable);

        List<QuizQuestionPublishedRecordData> result =
                page.getContent().stream()
                        .map(
                                i -> {
                                    QuizQuestionPublishedRecordData data =
                                            new QuizQuestionPublishedRecordData();
                                    data.setId(i.getId());
                                    data.setQuizNum(i.getQuizQuestion().getQuizNum());
                                    data.setTitle(i.getQuizQuestion().getTitle());
                                    data.setPublishedDate(i.getPublishedDate());
                                    return data;
                                })
                        .toList();

        return new PageData<>(number, size, page.getTotalElements(), result);
    }

    public QuizQuestionPublishedRecordResponse findPublishedRecordById(Long id)
            throws CustomException {

        QuizQuestionPublishedRecord publishedRecord =
                quizQuestionPublishedRecordRepository
                        .findById(id)
                        .orElseThrow(
                                () -> new CustomException(ErrorCode.REQUEST_ERROR_QUIZ_NOT_EXIST));
        QuizQuestionData quizQuestionData =
                QuizQuestionData.convertToRandomizedDto(publishedRecord);
        return new QuizQuestionPublishedRecordResponse(publishedRecord, quizQuestionData);
    }
}
