package point.common.service;

import java.math.BigDecimal;
import java.util.*;
import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import point.common.component.DataSourceManager;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.PointTransfer;
import point.common.entity.PointTransfer_;
import point.common.entity.PointUser;
import point.common.entity.PontaConvertWorker;
import point.common.exception.GameException;
import point.common.model.PointTransferResponseRowMapper;
import point.common.model.request.PontaForm;
import point.common.model.response.PageData;
import point.common.model.response.PointTransferResponse;
import point.common.ponta.PontaBizInVokerApi;
import point.common.ponta.TradeNumberGenerator;
import point.common.predicate.PointTransferPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointTransferService extends EntityService<PointTransfer, PointTransferPredicate> {

    private final PointTransferStatusHistoryService statusHistoryService;

    private final AssetService assetService;

    private final PontaBizInVokerApi pontaBizInVokerApi;

    private final PointUserService pointUserService;

    private final TradeNumberGenerator tradeNumberGenerator;

    private final DataSourceManager dataSourceManager;

    @Lazy private final PontaConvertWorkerService pontaConvertWorkerService;

    private static final String CC0281_TL_OPERATE = "106242";
    private static final String CC0281_TL_INVEST = "106240";
    private static final String RESPONSE_CODE_KEY = "ST0274_tl";
    private static final String FAILED_CODE_VALUE = "FAILED";
    private static final String ERROR_CODE_KEY = "E000000000";

    @Override
    public Class<PointTransfer> getEntityClass() {
        return PointTransfer.class;
    }

    public List<PointTransfer> findByCondition(
            Long userId,
            PointTransferStatusEnum status,
            PointTransferTypeEnum type,
            UserIdType userIdType,
            @Nullable HulftUploadStatusEnum uploadStatus) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<PointTransfer> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }
                        if (status != null) {
                            predicates.add(predicate.equalStatus(criteriaBuilder, root, status));
                        }
                        if (type != null) {
                            predicates.add(
                                    predicate.equalTransferType(criteriaBuilder, root, type));
                        }
                        if (userIdType != null) {
                            predicates.add(
                                    predicate.equalUserIdType(criteriaBuilder, root, userIdType));
                        }
                        if (uploadStatus != null) {
                            predicates.add(
                                    predicate.equalUploadStatus(
                                            criteriaBuilder, root, uploadStatus));
                        }
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(PointTransfer_.transferTime)));
                    }
                });
    }

    public PointTransfer findByCondition(
            Long userId,
            PointTransferStatusEnum status,
            PointTransferTypeEnum type,
            UserIdType userIdType,
            String tradeNumber) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public PointTransfer query() {
                        List<Predicate> predicates = new ArrayList<>();
                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }
                        if (status != null) {
                            predicates.add(predicate.equalStatus(criteriaBuilder, root, status));
                        }
                        if (type != null) {
                            predicates.add(
                                    predicate.equalTransferType(criteriaBuilder, root, type));
                        }
                        if (userIdType != null) {
                            predicates.add(
                                    predicate.equalUserIdType(criteriaBuilder, root, userIdType));
                        }
                        if (tradeNumber != null) {
                            predicates.add(
                                    predicate.equalTradeNumber(criteriaBuilder, root, tradeNumber));
                        }
                        return getSingleResult(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(PointTransfer_.transferTime)));
                    }
                });
    }

    public String transferPontaToOperation(
            Long userId,
            Long partnerId,
            UserIdType userIdType,
            BigDecimal pointAmount,
            PointTransferTypeEnum pointTransferTypeEnum)
            throws Exception {
        return customTransactionManager.execute(
                entityManager -> {
                    String tradeNumber = tradeNumberGenerator.generateTradeNumber();
                    // 1. 初期状態で PointTransfer レコードを作成
                    PointTransfer pointTransfer = new PointTransfer();
                    pointTransfer.setUserId(userId);
                    pointTransfer.setPartnerId(partnerId); // パートナー情報を設定
                    pointTransfer.setAmount(pointAmount);
                    pointTransfer.setUserIdType(userIdType); // PONTA のユーザーIDを設定
                    pointTransfer.setTransferType(pointTransferTypeEnum);
                    pointTransfer.setStatus(PointTransferStatusEnum.PROCESSING); // 初期状態は PROCESSING
                    pointTransfer.setTradeNumber(tradeNumber);
                    this.save(pointTransfer, entityManager);

                    // 2. 状態履歴を記録
                    statusHistoryService.recordStatusHistory(
                            pointTransfer,
                            null,
                            PointTransferStatusEnum.PROCESSING,
                            "振替処理中",
                            entityManager);

                    try {
                        PointUser pointUser = pointUserService.findOne(userId);

                        if (Objects.isNull(pointUser)) {
                            throw new GameException(
                                    ErrorCode.GAME_USER_NOT_FOUND,
                                    ErrorCode.GAME_USER_NOT_FOUND.getMessage());
                        }
                        pointTransfer.setRequestTime(new Date());
                        // 4. Ponta の API を呼び出して転送を実行
                        Map<String, String> resultsMap =
                                callPontaApi(
                                        pointUser.getPartnerMemberId(),
                                        pointAmount,
                                        tradeNumber,
                                        CC0281_TL_OPERATE);
                        if (resultsMap.containsKey(ERROR_CODE_KEY)) {
                            PontaConvertWorker worker =
                                    PontaConvertWorker.builder()
                                            .userId(userId)
                                            .tradeNumber(tradeNumber)
                                            .pointAmount(pointAmount)
                                            .contents(resultsMap.get(ERROR_CODE_KEY))
                                            .flag(0)
                                            .build();
                            pontaConvertWorkerService.save(worker);
                            return ERROR_CODE_KEY;
                        }
                        if (!PontaBizInVokerApi.isSuccess(resultsMap)) {
                            // 5. 転送成功の場合、状態を COMPLETED に更新
                            pointTransfer.setTransferTime(new Date());
                            pointTransfer.setStatus(PointTransferStatusEnum.COMPLETED);
                            this.save(pointTransfer, entityManager);
                            statusHistoryService.recordStatusHistory(
                                    pointTransfer,
                                    PointTransferStatusEnum.PROCESSING,
                                    PointTransferStatusEnum.COMPLETED,
                                    "振替処理完了",
                                    entityManager);
                            assetService.update(
                                    userId,
                                    Currency.JPY,
                                    pointAmount,
                                    BigDecimal.ZERO,
                                    entityManager);
                        } else {
                            // 6. 転送失敗の場合、状態を FAILED に更新
                            pointTransfer.setTransferTime(new Date());
                            pointTransfer.setStatus(PointTransferStatusEnum.FAILED);
                            this.save(pointTransfer, entityManager);
                            statusHistoryService.recordStatusHistory(
                                    pointTransfer,
                                    PointTransferStatusEnum.PROCESSING,
                                    PointTransferStatusEnum.FAILED,
                                    "振替処理失敗: Ponta API エラー" + resultsMap.get("ST0274_tl"),
                                    entityManager);
                        }
                        return resultsMap.get(RESPONSE_CODE_KEY);
                    } catch (Exception e) {
                        // 7. 例外が発生した場合、状態を FAILED に更新
                        pointTransfer.setStatus(PointTransferStatusEnum.FAILED);
                        this.save(pointTransfer, entityManager);
                        statusHistoryService.recordStatusHistory(
                                pointTransfer,
                                PointTransferStatusEnum.PROCESSING,
                                PointTransferStatusEnum.FAILED,
                                "転送失敗: システムエラー - " + e.getMessage(),
                                entityManager);
                        log.error("Ponta 転送中にエラーが発生しました", e);
                        throw new GameException(
                                ErrorCode.COMMON_ERROR_SYSTEM_ERROR,
                                ErrorCode.COMMON_ERROR_SYSTEM_ERROR.getMessage());
                    }
                });
    }

    private Map<String, String> callPontaApi(
            String memberId, BigDecimal pointAmount, String tradeNumber, String CC0281TL)
            throws GameException {
        // ここに Ponta の API 呼び出しロジックを実装
        PontaForm pontaForm =
                PontaForm.builder()
                        .memberId(memberId)
                        .type(PointTransactionType.POINT_USING)
                        .tradeNumber(tradeNumber)
                        .pointAmount(pointAmount.toString())
                        .CC0281_TL(CC0281TL)
                        .build();
        return pontaBizInVokerApi.if003(pontaForm);
    }

    public PageData<PointTransfer> findByConditionPage(
            Long userId,
            String partnerName,
            Long partnerId,
            String partnerMemberId,
            PointTransferTypeEnum transferType,
            PointTransferStatusEnum status,
            Long dateFrom,
            Long dateTo,
            UserIdType userIdType,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                partnerName,
                                                partnerId,
                                                partnerMemberId,
                                                transferType,
                                                status,
                                                dateFrom,
                                                dateTo,
                                                userIdType));
                            }
                        });

        return new PageData<PointTransfer>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PointTransfer, List<PointTransfer>>() {
                            @Override
                            public List<PointTransfer> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                partnerName,
                                                partnerId,
                                                partnerMemberId,
                                                transferType,
                                                status,
                                                dateFrom,
                                                dateTo,
                                                userIdType),
                                        number,
                                        size,
                                        Objects.equals(userIdType, UserIdType.Operate)
                                                ? new Order[] {
                                                    criteriaBuilder.desc(
                                                            root.get(PointTransfer_.requestTime))
                                                }
                                                : new Order[] {
                                                    criteriaBuilder.desc(
                                                            root.get(PointTransfer_.UPDATED_AT)),
                                                    criteriaBuilder.asc(
                                                            root.get(PointTransfer_.partnerId)),
                                                    criteriaBuilder.asc(
                                                            root.get(PointTransfer_.userId))
                                                });
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<PointTransfer> root,
            Long userId,
            String partnerName,
            Long partnerId,
            String partnerMemberId,
            PointTransferTypeEnum transferType,
            PointTransferStatusEnum status,
            Long dateFrom,
            Long dateTo,
            UserIdType userIdType) {
        List<Predicate> predicates = new ArrayList<>();
        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }
        if (StringUtils.isNotBlank(partnerName)) {
            predicates.add(
                    predicate.equalPartnerName(criteriaBuilder, root, partnerName, userIdType));
        }
        if (partnerId != null) {
            predicates.add(predicate.equalPartnerId(criteriaBuilder, root, partnerId, userIdType));
        }
        if (StringUtils.isNotBlank(partnerMemberId)) {
            predicates.add(
                    predicate.equalPartnerMemberId(
                            criteriaBuilder, root, partnerMemberId, userIdType));
        }
        if (status != null) {
            predicates.add(predicate.equalStatus(criteriaBuilder, root, status));
        }
        if (dateFrom != null) {
            predicates.add(
                    Objects.equals(userIdType, UserIdType.Operate)
                            ? predicate.greaterThanOrEqualToCreatedAt(
                                    criteriaBuilder, root, new Date(dateFrom))
                            : predicate.greaterThanOrEqualToUpdatedAt(
                                    criteriaBuilder, root, new Date(dateFrom)));
        }
        if (dateTo != null) {
            predicates.add(
                    Objects.equals(userIdType, UserIdType.Operate)
                            ? predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo))
                            : predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(dateTo)));
        }
        if (userIdType != null) {
            predicates.add(predicate.equalUserIdType(criteriaBuilder, root, userIdType));
        }
        if (transferType != null) {
            predicates.add(predicate.equalTransferType(criteriaBuilder, root, transferType));
        }
        return predicates;
    }

    public PageData<PointTransfer> findByConditionPage(Long userId, Integer number, Integer size)
            throws Exception {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null));
                            }
                        });

        return new PageData<PointTransfer>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<PointTransfer> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null),
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(PointTransfer_.createdAt)));
                            }
                        }));
    }

    public void transferOperationToPonta(
            Long userId,
            Long partnerId,
            UserIdType userIdType,
            BigDecimal pointAmount,
            BigDecimal pointAmountFee,
            PointTransferTypeEnum pointTransferTypeEnum)
            throws Exception {
        customTransactionManager.execute(
                entityManager -> {
                    String tradeNumber = tradeNumberGenerator.generateTradeNumber();
                    // 1. 初期状態で PointTransfer レコードを作成
                    PointTransfer pointTransfer = new PointTransfer();
                    pointTransfer.setUserId(userId);
                    pointTransfer.setPartnerId(partnerId); // パートナー情報を設定
                    pointTransfer.setAmount(pointAmount);
                    pointTransfer.setFee(pointAmountFee);
                    pointTransfer.setUserIdType(userIdType); // PONTA のユーザーIDを設定
                    pointTransfer.setTransferType(pointTransferTypeEnum);
                    pointTransfer.setStatus(PointTransferStatusEnum.PROCESSING); // 初期状態は PROCESSING
                    pointTransfer.setUploadStatus(HulftUploadStatusEnum.PENDING);
                    pointTransfer.setTradeNumber(tradeNumber);
                    pointTransfer.setRequestTime(new Date());
                    this.save(pointTransfer, entityManager);
                    // 2. 状態履歴を記録
                    statusHistoryService.recordStatusHistory(
                            pointTransfer,
                            null,
                            PointTransferStatusEnum.PROCESSING,
                            "振替処理中",
                            entityManager);
                    BigDecimal lockedAmount =
                            Optional.ofNullable(pointAmount)
                                    .orElse(BigDecimal.ZERO)
                                    .add(
                                            Optional.ofNullable(pointAmountFee)
                                                    .orElse(BigDecimal.ZERO));
                    assetService.update(
                            userId, Currency.JPY, BigDecimal.ZERO, lockedAmount, entityManager);
                });
    }

    public String transferPontaToInvestiment(
            Long userId,
            Long partnerId,
            UserIdType userIdType,
            BigDecimal pointAmount,
            PointTransferTypeEnum pointTransferTypeEnum)
            throws Exception {

        return customTransactionManager.execute(
                entityManager -> {
                    String tradeNumber = tradeNumberGenerator.generateTradeNumber();
                    // 1. 初期状態で PointTransfer レコードを作成
                    PointTransfer pointTransfer = new PointTransfer();
                    pointTransfer.setUserId(userId);
                    pointTransfer.setPartnerId(partnerId); // パートナー情報を設定
                    pointTransfer.setAmount(pointAmount);
                    pointTransfer.setUserIdType(userIdType); // PONTA のユーザーIDを設定
                    pointTransfer.setTransferType(pointTransferTypeEnum);
                    pointTransfer.setStatus(PointTransferStatusEnum.PROCESSING); // 初期状態は PROCESSING
                    pointTransfer.setTradeNumber(tradeNumber);
                    this.save(pointTransfer, entityManager);

                    // 2. 状態履歴を記録
                    statusHistoryService.recordStatusHistory(
                            pointTransfer,
                            null,
                            PointTransferStatusEnum.PROCESSING,
                            "振替処理中",
                            entityManager);

                    try {
                        PointUser pointUser = pointUserService.findByUserId(userId);
                        if (Objects.isNull(pointUser)) {
                            throw new GameException(
                                    ErrorCode.GAME_USER_NOT_FOUND,
                                    ErrorCode.GAME_USER_NOT_FOUND.getMessage());
                        }

                        // 4. Ponta の API を呼び出して転送を実行
                        pointTransfer.setRequestTime(new Date());
                        Map<String, String> resultsMap =
                                callPontaApi(
                                        pointUser.getPartnerMemberId(),
                                        pointAmount,
                                        tradeNumber,
                                        CC0281_TL_INVEST);
                        if (!PontaBizInVokerApi.isSuccess(resultsMap)) {
                            // 5. 転送成功の場合、状態を COMPLETED に更新
                            pointTransfer.setTransferTime(new Date());
                            pointTransfer.setStatus(PointTransferStatusEnum.COMPLETED);
                            this.save(pointTransfer, entityManager);
                            statusHistoryService.recordStatusHistory(
                                    pointTransfer,
                                    PointTransferStatusEnum.PROCESSING,
                                    PointTransferStatusEnum.COMPLETED,
                                    "振替処理完了",
                                    entityManager);
                            assetService.update(
                                    userId,
                                    Currency.POINT,
                                    pointAmount,
                                    BigDecimal.ZERO,
                                    entityManager);
                        } else {
                            // 6. 転送失敗の場合、状態を FAILED に更新
                            pointTransfer.setTransferTime(new Date());
                            pointTransfer.setStatus(PointTransferStatusEnum.FAILED);
                            this.save(pointTransfer, entityManager);
                            statusHistoryService.recordStatusHistory(
                                    pointTransfer,
                                    PointTransferStatusEnum.PROCESSING,
                                    PointTransferStatusEnum.FAILED,
                                    "振替処理失敗: Ponta API エラー" + resultsMap.get("ST0274_tl"),
                                    entityManager);
                        }
                        return resultsMap.get(RESPONSE_CODE_KEY);
                    } catch (Exception e) {
                        // 7. 例外が発生した場合、状態を FAILED に更新
                        pointTransfer.setStatus(PointTransferStatusEnum.FAILED);
                        this.save(pointTransfer, entityManager);
                        statusHistoryService.recordStatusHistory(
                                pointTransfer,
                                PointTransferStatusEnum.PROCESSING,
                                PointTransferStatusEnum.FAILED,
                                "転送失敗: システムエラー - " + e.getMessage(),
                                entityManager);
                        log.error("Ponta 転送中にエラーが発生しました", e);
                        throw new GameException(
                                ErrorCode.COMMON_ERROR_SYSTEM_ERROR,
                                ErrorCode.COMMON_ERROR_SYSTEM_ERROR.getMessage());
                    }
                });
    }

    public void transferOperationToPontaSuccess(
            String partnerMemberId, String tradeNUmber, EntityManager entityManager)
            throws Exception {
        log.info("partnerMemberId:{}, tradeNUmber: {}", partnerMemberId, tradeNUmber);
        PointUser pointUser = pointUserService.findByPartnerMemberId(partnerMemberId);
        if (Objects.isNull(pointUser)) {
            log.info("partnerMemberId is not null");
            return;
        }
        PointTransfer pointTransfer =
                this.findByCondition(
                        pointUser.getId(),
                        PointTransferStatusEnum.PROCESSING,
                        PointTransferTypeEnum.OUT,
                        UserIdType.Operate,
                        tradeNUmber);
        if (pointTransfer.getStatus() == PointTransferStatusEnum.PROCESSING) {
            // 5. 転送成功の場合、状態を COMPLETED に更新
            pointTransfer.setTransferTime(new Date());
            pointTransfer.setUploadStatus(HulftUploadStatusEnum.COMPLETED);
            pointTransfer.setStatus(PointTransferStatusEnum.COMPLETED);
            BigDecimal amount =
                    pointTransfer
                            .getAmount()
                            .add(
                                    Optional.ofNullable(pointTransfer.getFee())
                                            .orElse(BigDecimal.ZERO));
            this.save(pointTransfer, entityManager);
            statusHistoryService.recordStatusHistory(
                    pointTransfer,
                    PointTransferStatusEnum.PROCESSING,
                    PointTransferStatusEnum.COMPLETED,
                    "振替処理完了",
                    entityManager);
            assetService.update(
                    pointTransfer.getUserId(),
                    Currency.JPY,
                    amount.negate(),
                    amount.negate(),
                    entityManager);
        }
    }

    public void transferOperationToPontaFailed(
            String partnerMemberId,
            String tradeNUmber,
            String pontaErrorCode,
            EntityManager entityManager)
            throws Exception {
        PointUser pointUser = pointUserService.findByPartnerMemberId(partnerMemberId);
        PointTransfer pointTransfer =
                this.findByCondition(
                        pointUser.getId(),
                        PointTransferStatusEnum.PROCESSING,
                        PointTransferTypeEnum.OUT,
                        UserIdType.Operate,
                        tradeNUmber);

        if (Objects.isNull(pointTransfer)) {
            PointTransfer completedPointTransfer =
                    this.findByCondition(
                            pointUser.getId(),
                            PointTransferStatusEnum.COMPLETED,
                            PointTransferTypeEnum.OUT,
                            UserIdType.Operate,
                            tradeNUmber);
            if (Objects.isNull(completedPointTransfer)) {
                log.error("No PointTransfer found by tradeNumber: {}", tradeNUmber);
            } else {
                log.info(
                        "The PointTransfer is already completed, transferNumber: {}, pointTransferId: {}. This log could mean a duplicate transaction. Please see logs for details.",
                        tradeNUmber,
                        completedPointTransfer.getId());
            }
            return;
        }

        pointTransfer.setTransferTime(new Date());
        pointTransfer.setStatus(PointTransferStatusEnum.FAILED);
        pointTransfer.setUploadStatus(HulftUploadStatusEnum.FAILED);
        this.save(pointTransfer, entityManager);
        statusHistoryService.recordStatusHistory(
                pointTransfer,
                PointTransferStatusEnum.PROCESSING,
                PointTransferStatusEnum.FAILED,
                "振替処理失敗: Ponta API エラー error code:" + pontaErrorCode,
                entityManager);
    }

    public void transferOperationToPontaSuccess_m(
            String partnerMemberId, String tradeNUmber, EntityManager entityManager)
            throws Exception {

        PointUser pointUser = pointUserService.findByPartnerMemberId(partnerMemberId);
        if (Objects.isNull(pointUser)) {
            log.info("transferOperationToPontaSuccess_M pointUser is not null");
            return;
        }
        PointTransfer pointTransfer =
                this.findByCondition(
                        pointUser.getId(),
                        PointTransferStatusEnum.PROCESSING,
                        PointTransferTypeEnum.OUT,
                        UserIdType.Operate,
                        tradeNUmber);
        if (pointTransfer.getStatus() == PointTransferStatusEnum.PROCESSING) {
            // 5. 転送成功の場合、状態を COMPLETED に更新
            pointTransfer.setTransferTime(new Date());
            pointTransfer.setStatus(PointTransferStatusEnum.COMPLETED);
            pointTransfer.setUploadStatus(HulftUploadStatusEnum.COMPLETED);
            this.save(pointTransfer, entityManager);
            statusHistoryService.recordStatusHistory(
                    pointTransfer,
                    PointTransferStatusEnum.PROCESSING,
                    PointTransferStatusEnum.COMPLETED,
                    "振替処理完了",
                    entityManager);
        }
    }

    /** Updates the status of a PointTransfer by its trade number. */
    public PointTransfer updateStatusByTradeNumber(
            String tradeNumber, String code, EntityManager entityManager) throws Exception {
        // Find the existing transfer by trade number
        Optional<PointTransfer> transferOptional = findByTradeNumber(tradeNumber, entityManager);

        if (transferOptional.isEmpty()) {
            log.info("Point transfer not found with trade number: " + tradeNumber);
            throw new GameException(
                    ErrorCode.PONTA_REQUEST_WEB_IF_003_ERROR,
                    ErrorCode.PONTA_REQUEST_WEB_IF_003_ERROR.getMessage());
        }
        PointTransfer pointTransfer = transferOptional.get();
        pointTransfer.setStatus(
                FAILED_CODE_VALUE.equals(code)
                        ? PointTransferStatusEnum.FAILED
                        : PointTransferStatusEnum.COMPLETED);
        pointTransfer.setTransferTime(new Date());
        return save(pointTransfer, entityManager);
    }

    /** Helper method to find a PointTransfer by trade number with EntityManager */
    private Optional<PointTransfer> findByTradeNumber(
            String tradeNumber, EntityManager pointTransferentityManager) {
        PointTransfer pointTransfer =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public PointTransfer query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.equalTradeNumber(
                                                criteriaBuilder, root, tradeNumber));
                                return getSingleResult(
                                        pointTransferentityManager,
                                        criteriaQuery,
                                        root,
                                        predicates);
                            }
                        });
        return Optional.ofNullable(pointTransfer);
    }

    /**
     * Find combined exchange history from both point_transfer and choice_p_withdrawal tables using
     * custom SQL with proper sorting and pagination at database level
     */
    public PageData<PointTransferResponse> findCombinedExchangeHistory(
            Long userId, Integer number, Integer size) {
        // First get the total count
        String countSql =
                "SELECT COUNT(*) FROM ("
                        + "SELECT id FROM point_transfer WHERE user_id = :userId "
                        + "UNION ALL "
                        + "SELECT id FROM choice_p_withdrawal WHERE user_id = :userId"
                        + ") AS combined";

        MapSqlParameterSource countParams = new MapSqlParameterSource();
        countParams.addValue("userId", userId);

        NamedParameterJdbcTemplate namedTemplate =
                new NamedParameterJdbcTemplate(dataSourceManager.getMasterJdbcTemplate());

        log.info("countSql:{}", countSql);
        Long totalCount = namedTemplate.queryForObject(countSql, countParams, Long.class);

        // Build the main query with UNION ALL and proper sorting
        String sql =
                "SELECT * FROM ("
                        + "SELECT "
                        + "id, user_id, id_type as user_id_type, transfer_type, amount, "
                        + "status, request_time, transfer_time, created_at, 'POINT_TRANSFER' as source_type "
                        + "FROM point_transfer WHERE user_id = :userId "
                        + "UNION ALL "
                        + "SELECT "
                        + "id, user_id, 'OPERATE' as user_id_type, 'P withdrawal' as transfer_type, amount, "
                        + "'COMPLETED' as status, withdraw_time as request_time, withdraw_time as transfer_time, "
                        + "created_at, 'CHOICE_WITHDRAWAL' as source_type "
                        + "FROM choice_p_withdrawal WHERE user_id = :userId"
                        + ") AS combined_history "
                        + "ORDER BY created_at DESC "
                        + "LIMIT :size OFFSET :offset";

        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("userId", userId);
        params.addValue("size", size);
        params.addValue("offset", number * size);

        List<PointTransferResponse> results =
                namedTemplate.query(sql, params, new PointTransferResponseRowMapper());
        log.info("sql:{}", sql);
        log.info("results:{}", results);
        return new PageData<>(number, size, totalCount != null ? totalCount : 0, results);
    }
}
