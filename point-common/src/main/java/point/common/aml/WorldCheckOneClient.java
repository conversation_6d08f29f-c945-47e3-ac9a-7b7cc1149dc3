package point.common.aml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.Key;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.crypto.spec.SecretKeySpec;
import javax.ws.rs.BadRequestException;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.tomitribe.auth.signatures.Signature;
import org.tomitribe.auth.signatures.Signer;
import point.common.constant.KycData;
import point.common.util.CharUtil;

@Slf4j
public class WorldCheckOneClient {

    private static WorldCheckOneClient _client;

    /** WC1のホスト名 */
    private final String host;

    /** WC1のAPIキー */
    private final String apiKey;

    /** WC1のシークレットキー */
    private final String secretKey;

    /** 反社チェックに使用するgroup */
    private Group group;

    /** アカウントに作成されているcasd template (groupId -> case template) */
    private Map<String, CaseTemplate> caseTemplates;

    /** デジタル署名ジェネレータ */
    private final SignedHeaderGenerator signedHeaderGenerator;

    private final Client client;

    private final ObjectMapper mapper;

    /** 生年月日の secondary field のtypeId */
    private static String BIRTHDAY_TYPE_ID = "SFCT_2";

    private static String GENDER_TYPE_ID = "SFCT_1";

    /** World Check OneへのAPIリクエストのタイムアウト(秒) */
    private static int TIMEOUT_SECONDS = 30;

    private WorldCheckOneClient(String host, String apiKey, String secretKey) {
        this.host = host;
        this.apiKey = apiKey;
        this.secretKey = secretKey;
        this.client = createClient();
        this.signedHeaderGenerator = new SignedHeaderGenerator(host, apiKey, secretKey);
        this.mapper = new ObjectMapper();
    }

    /** WC1へのAPIリクエストに使用するクライアントを作成します */
    private Client createClient() {
        return ClientBuilder.newBuilder()
                .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .build();
    }

    /**
     * World Check One(WC1)のクライアントを作成します。 <br>
     * このメソッドでリスクチェック依頼に必要なデータ(groupおよびcase template)の取得まで行い、リスクチェックの依頼ができる状態にします。
     *
     * @param host WC1のホスト名
     * @param apiKey WC1のAPIキー
     * @param secretKey WC1のシークレットキー
     * @return WC1クライアント
     */
    public static synchronized WorldCheckOneClient create(
            String host, String apiKey, String secretKey, String groupId) {
        if (_client == null) {
            WorldCheckOneClient client = new WorldCheckOneClient(host, apiKey, secretKey);
            client.initialize(groupId);
            _client = client;
        }
        return _client;
    }

    /** 反社チェックのリクエストに必要となるデータをWC1から取得します */
    private void initialize(String groupId) {
        // groupをWC1から全件取得し、設定ファイルで指定されたgroupIdで絞る
        // 以後の処理ではこのgroupを使う
        List<Group> groups = fetchGroups();
        Group fetchGroup =
                groups.stream()
                        .filter(g -> g.getId().equals(groupId))
                        .findFirst()
                        .orElseThrow(
                                () ->
                                        new WorldCheckOneException(
                                                String.format(
                                                        "group does not exist: groupId=%s",
                                                        groupId)));
        // groupに作成されたcase templateを全件取得
        this.caseTemplates = Map.of(groupId, fetchCaseTemplate(groupId));
        this.group = fetchGroup;
    }

    /** WC1にAPIコールし、レスポンスのJSONを返します。 */
    private String request(WorldCheckOneAPI api) {
        return request(api, null);
    }

    /** groupのリストをWC1から取得します。 */
    private List<Group> fetchGroups() {
        try {
            WorldCheckOneAPI api =
                    WorldCheckOneAPI.builder().type(WorldCheckOneAPIType.GET_GROUPS).build();
            String response = request(api);
            log.info("fetchGroups response: {}", response);
            return Arrays.asList(mapper.readValue(response, Group[].class));
        } catch (IOException e) {
            throw new WorldCheckOneException(e);
        }
    }

    /** case templateをWC1から取得します。 */
    private CaseTemplate fetchCaseTemplate(String groupId) {
        try {
            WorldCheckOneAPI api =
                    WorldCheckOneAPI.builder()
                            .type(WorldCheckOneAPIType.GET_CASE_TEMPLATE)
                            .groupId(groupId)
                            .build();
            String response = request(api);
            return mapper.readValue(response, CaseTemplate.class);
        } catch (IOException e) {
            throw new WorldCheckOneException(e);
        }
    }

    /**
     * World Check Oneに反社チェックをリクエストし、その結果を返します。
     *
     * @param req 反社チェックリクエスト
     * @return 反社チェック結果
     */
    public WorldCheckOneRes requestScreening(WorldCheckOneReq req) {
        try {
            // 反社チェックAPIのリクエスト送信
            String groupId = this.group.getId();
            ScreeningRequest request = ScreeningRequest.create(req, groupId);
            String screeningRequestBody = mapper.writeValueAsString(request);
            log.info("sending screening request to WC1: {}", screeningRequestBody);

            WorldCheckOneAPI screeningApi =
                    WorldCheckOneAPI.builder().type(WorldCheckOneAPIType.REQUEST_SCREENING).build();
            String screeningResultBody =
                    request(screeningApi, CharUtil.toDbc(screeningRequestBody));
            ScreeningResult screeningResult =
                    mapper.readValue(screeningResultBody, ScreeningResult.class);
            log.info("screening result received: {}", screeningResultBody);
            return WorldCheckOneRes.builder()
                    .kycData(screeningResult.getKycData())
                    .caseSystemId(screeningResult.getCaseSystemId())
                    .caseId(screeningResult.getCaseId())
                    .customerAttribute(req.getCustomerAttribute())
                    .build();
        } catch (IOException e) {
            throw new WorldCheckOneException(e);
        }
    }

    /** WC1にAPIコールし、レスポンスのJSONを返します。 */
    private String request(WorldCheckOneAPI api, String requestBody) {
        String url = "https://" + host;
        log.info("requestUrl: {}", url);
        WebTarget target = client.target(url).path(api.getPath());
        for (Map.Entry<String, String> param : api.getQueryParams().entrySet()) {
            target = target.queryParam(param.getKey(), param.getValue());
        }

        String method = api.getMethod().toLowerCase();
        MultivaluedMap<String, Object> headers = signedHeaderGenerator.generate(api, requestBody);
        log.info("requestBody: {}", Entity.json(requestBody));
        log.info("requestHeaders: {}", headers);
        log.info("requestMethod: {}", method);

        String response = null;
        try {
            switch (method) {
                case "get":
                    response = target.request().headers(headers).get(String.class);
                    break;
                case "post":
                    response =
                            target.request()
                                    .headers(headers)
                                    .post(Entity.json(requestBody), String.class);
                    break;
                case "put":
                    response =
                            target.request()
                                    .headers(headers)
                                    .put(Entity.json(requestBody), String.class);
                    break;
                case "delete":
                    response = target.request().headers(headers).delete(String.class);
                    break;
                default:
                    throw new UnsupportedOperationException();
            }
        } catch (BadRequestException e) {
            log.warn(
                    "received error response from WC1: error message={}, error response={}",
                    e.getMessage(),
                    e.getResponse().toString());
            throw new WorldCheckOneException(e);
        } finally {
            log.info(
                    "WC1 request result: \n    URL=[{}], \n    REQ-HEADERS=[{}], \n    REQ-BODY=[{}], \n    RESPONSE=[{}]",
                    target.getUri(),
                    headers,
                    requestBody,
                    response);
        }
        return response;
    }

    /**
     * デジタル署名付きのHTTPリクエストヘッダを生成するクラス <br>
     * 必要なヘッダを追加した上で<a href=
     * "https://www.ietf.org/archive/id/draft-cavage-http-signatures-05.txt">HTTP Signature</a>を生成して
     * HMAC-SHA256でシグネチャを作成し、Authorizationヘッダを付加します。 <br>
     * <a href="https://github.com/tomitribe/http-signatures-java">HTTP Signatures Java
     * Client</a>を利用します。
     */
    public static class SignedHeaderGenerator {

        /** Dateヘッダ用のformatter */
        private static final SimpleDateFormat DATE_FORMAT;

        /** シグネチャを作成するアルゴリズム。WC1仕様で指定されたもの */
        private static final String SIGNATURE_ALGORITHM = "hmac-sha256";

        /** デジタル署名のために必要となるヘッダ (Methodごと) */
        private static final Map<String, List<String>> REQUIRED_HEADERS;

        static {
            DATE_FORMAT = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss zzz", Locale.US);
            DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("GMT"));
            REQUIRED_HEADERS =
                    Map.of(
                            "get",
                            List.of("(request-target)", "host", "date"),
                            "post",
                            List.of(
                                    "(request-target)",
                                    "host",
                                    "date",
                                    "content-type",
                                    "content-length"),
                            "put",
                            List.of(
                                    "(request-target)",
                                    "host",
                                    "date",
                                    "content-type",
                                    "content-length"),
                            "delete",
                            List.of("(request-target)", "host", "date"));
        }

        private final String host;

        private final Map<String, Signer> signers;

        /**
         * コンストラクタ
         *
         * @param host WC1のホスト名
         * @param apiKey WC1のAPIキー
         * @param secretKey WC1のシークレットキー
         */
        SignedHeaderGenerator(String host, String apiKey, String secretKey) {
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
            this.host = host;
            this.signers =
                    REQUIRED_HEADERS.entrySet().stream()
                            .collect(
                                    Collectors.toMap(
                                            Map.Entry::getKey,
                                            entry ->
                                                    buildSigner(
                                                            apiKey,
                                                            secretKeySpec,
                                                            entry.getKey())));
        }

        /**
         * デジタル署名付きのHTTPヘッダを作成します。
         *
         * @param api API
         * @return HTTPヘッダ
         */
        MultivaluedMap<String, Object> generate(WorldCheckOneAPI api) {
            return generate(api, null);
        }

        /**
         * デジタル署名付きのHTTPヘッダを作成します。
         *
         * @param api API
         * @param requestBody request body
         * @return HTTPヘッダ
         */
        MultivaluedMap<String, Object> generate(WorldCheckOneAPI api, String requestBody) {
            final String method = api.getMethod().toLowerCase();
            final String path = api.getPath();

            MultivaluedMap<String, Object> headers = new MultivaluedHashMap<>();
            headers.putSingle("date", DATE_FORMAT.format(new Date()));
            headers.putSingle("host", host);
            if (method.equals("put") || method.equals("post")) {
                headers.putSingle("content-type", "application/json");
                try {
                    headers.putSingle(
                            "content-length",
                            Integer.toString(requestBody.getBytes("UTF-8").length));
                } catch (UnsupportedEncodingException e) {
                    log.info(" request body getBytes() failed error: {}", e.getMessage());
                }
            }

            final String signature = this.calculateSignature(method, path, headers, requestBody);
            headers.putSingle("Authorization", signature);
            return headers;
        }

        private Signer buildSigner(String apiKey, Key secretKey, String method) {
            final Signature signature =
                    new Signature(
                            apiKey,
                            SIGNATURE_ALGORITHM,
                            null,
                            REQUIRED_HEADERS.get(method.toLowerCase()));
            return new WorldCheckOneSigner(secretKey, signature);
        }

        private String calculateSignature(
                String method,
                String path,
                MultivaluedMap<String, Object> headers,
                String requestBody) {
            Signer signer = this.signers.get(method);

            MultivaluedHashMap<String, Object> newHeaders = new MultivaluedHashMap<>();
            newHeaders.putAll(headers);
            if (Strings.isNotEmpty(requestBody)) {
                newHeaders.putSingle("body", requestBody);
            }

            Map<String, String> signingHeaders =
                    newHeaders.keySet().stream()
                            .collect(
                                    Collectors.toMap(
                                            key -> key,
                                            key -> newHeaders.get(key).get(0).toString()));

            try {
                log.info(signer.createSigningString(method, path, signingHeaders));
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {

                return signer.sign(method, path, signingHeaders).toString();
            } catch (IOException e) {
                throw new WorldCheckOneException("Failed to generate signature", e);
            }
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class Group {

        private String id;

        private String name;

        private String parentId;

        private String hasChildren;

        private String status;

        private List<Group> children;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class CaseTemplate {

        private String groupId;

        private String groupScreeningType;

        private List<FieldDefinition> customFields;

        private SecondaryFieldsByProvider secondaryFieldsByProvider;

        private List<String> mandatoryProviderTypes;

        private NameTransposition nameTransposition;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class SecondaryFieldsByProvider {

        private SecondaryFieldsHolder watchlist;

        private SecondaryFieldsHolder clientWatchlist;

        private SecondaryFieldsHolder passportCheck;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class SecondaryFieldsHolder {

        private SecondaryFieldsByEntity secondaryFieldsByEntity;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class SecondaryFieldsByEntity {

        private List<FieldDefinition> individual;

        private List<FieldDefinition> vessel;

        private List<FieldDefinition> organisation;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class FieldDefinition {

        private String typeId;

        private String fieldValueType;

        private String regExp;

        private String fieldRequired;

        private String label;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class NameTransposition {

        private String selected;

        private String type;

        private String available;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ScreeningRequest {

        private String groupId;

        private String entityType;

        private String name;

        private List<String> providerTypes;

        private List<FieldValue> secondaryFields;

        private List<FieldValue> customFields;

        private static ScreeningRequest create(WorldCheckOneReq req, String groupId) {
            String entityType = req.getType().name(); // "INDIVIDUAL" or "ORGANISATION"
            List<String> providerTypes = Collections.singletonList("WATCHLIST"); // ["WATCHLIST"]固定
            List<FieldValue> secondaryFields = new ArrayList<>();
            List<FieldValue> customFields = Collections.emptyList(); // Simonでは使用しない

            if (StringUtils.isNotEmpty(req.getGender())) {
                secondaryFields.add(new FieldValue(GENDER_TYPE_ID, req.getGender(), null));
            }

            // 生年月日が指定されている場合は、secondary fieldとしてパラメタに追加する
            if (req.getBirthday() != null) {
                String birthDay = DateTimeFormatter.ISO_LOCAL_DATE.format(req.getBirthday()); // ISO
                // 8601
                // format
                secondaryFields.add(new FieldValue(BIRTHDAY_TYPE_ID, null, birthDay));
            }

            return new ScreeningRequest(
                    groupId,
                    entityType,
                    req.getName(),
                    providerTypes,
                    secondaryFields,
                    customFields);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private static class FieldValue {

        private String typeId;

        private String value; // 日付型以外で使用

        private String dateTimeValue; // 日付型の場合のみ使用
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class ScreeningResult {

        private String caseId;

        private String name;

        private List<String> providerTypes;

        private Object customFields;

        private Object secondaryFields;

        private String groupId;

        private String entityType;

        private String caseSystemId;

        private Object caseScreeningState;

        private String lifecycleState;

        private Object creator;

        private Object modifier;

        private Object assignee;

        private Object creationDate;

        private Object modificationDate;

        private boolean nameTransposition;

        private boolean outstandingActions;

        private List<WatchlistScreeningResult> results;

        private KycData getKycData() {
            // 結果が1件でもあれば、matchStrengthなどの値に関わらずKycData.EXISTSを返す
            return results.isEmpty() ? KycData.NONE : KycData.EXISTS;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class WatchlistScreeningResult {

        private String resultId;

        private String referenceId;

        private String matchStrength;

        private String matchedTerm;

        private String submittedTerm;

        private String matchedNameType;

        private String category;

        private String providerType;

        private String gender;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class CaseReference {

        private String caseId;

        private String caseSystemId;
    }
}
