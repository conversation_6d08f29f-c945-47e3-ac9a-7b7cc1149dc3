/**
 * Copyright (c) 2019, toiware Inc.<br>
 * All rights reserved
 */
package point.common.aml;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.util.Strings;

/** World Check One API */
@Value
@Builder
@AllArgsConstructor
public class WorldCheckOneAPI {

    private WorldCheckOneAPIType type;

    private String groupId;

    private String caseId;

    /**
     * APIのMethodを取得します。
     *
     * @return Method
     */
    public String getMethod() {
        return type.getMethod();
    }

    /**
     * APIのパスを取得します。
     *
     * @return パス
     */
    public String getPath() {
        Map<String, String> values = new HashMap<>(1);
        values.put("groupId", groupId);
        StringSubstitutor sub = new StringSubstitutor(values, "%{", "}");
        return sub.replace(type.getPath());
    }

    /**
     * APIのGETパラメタを取得します。
     *
     * @return パス GETパラメタ
     */
    public Map<String, String> getQueryParams() {
        if (Strings.isNotEmpty(caseId)) {
            return Map.of("caseId", caseId);
        }
        return Collections.emptyMap();
    }
}
