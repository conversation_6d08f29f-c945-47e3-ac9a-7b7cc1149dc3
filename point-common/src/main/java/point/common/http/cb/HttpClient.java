package point.common.http.cb;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPInputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectionPoolTimeoutException;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpClient {

    private static final String SERVER_VERSION = "Java-HttpClient:1.0.0";
    private static final String UTF_8 = "UTF-8";

    private static final int TIME_OUT_MILLIS = 10000;

    private static final int CON_TIME_OUT_MILLIS = 5000;

    private static final int SOC_TIME_OUT_MILLIS = 15000;

    private static final CloseableHttpClient postClient;

    static {
        HttpClientBuilder builder = HttpClients.custom();
        builder.setUserAgent(SERVER_VERSION);
        builder.setConnectionTimeToLive(CON_TIME_OUT_MILLIS, TimeUnit.MILLISECONDS);
        builder.setMaxConnPerRoute(-1);
        builder.setMaxConnTotal(-1);
        builder.disableAutomaticRetries();

        postClient = builder.build();
    }

    /**
     * Request http delete method.
     *
     * @param url url
     * @param headers headers
     * @param paramValues params
     * @return {@link HttpResult} as response
     */
    public static HttpResult httpDelete(
            String url, List<String> headers, Map<String, String> paramValues) {
        return request(
                url,
                headers,
                paramValues,
                StringUtils.EMPTY,
                CON_TIME_OUT_MILLIS,
                TIME_OUT_MILLIS,
                UTF_8,
                HttpMethod.DELETE);
    }

    /**
     * Request http get method.
     *
     * @param url url
     * @param headers headers
     * @param paramValues params
     * @return {@link HttpResult} as response
     */
    public static HttpResult httpGet(
            String url, List<String> headers, Map<String, String> paramValues) {
        return request(
                url,
                headers,
                paramValues,
                StringUtils.EMPTY,
                CON_TIME_OUT_MILLIS,
                TIME_OUT_MILLIS,
                UTF_8,
                HttpMethod.GET);
    }

    /**
     * Do http request.
     *
     * @param url request url
     * @param headers request headers
     * @param paramValues request params
     * @param body request body
     * @param connectTimeout timeout of connection
     * @param readTimeout timeout of request
     * @param encoding charset of request
     * @param method http method
     * @return {@link HttpResult} as response
     */
    public static HttpResult request(
            String url,
            List<String> headers,
            Map<String, String> paramValues,
            String body,
            int connectTimeout,
            int readTimeout,
            String encoding,
            String method) {
        HttpURLConnection conn = null;
        try {
            String encodedContent = encodingParams(paramValues, encoding);
            url += StringUtils.isBlank(encodedContent) ? StringUtils.EMPTY : ("?" + encodedContent);

            conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setConnectTimeout(connectTimeout);
            conn.setReadTimeout(readTimeout);
            conn.setRequestMethod(method);

            setHeaders(conn, headers, encoding);

            if (StringUtils.isNotBlank(body)) {
                conn.setDoOutput(true);
                byte[] b = body.getBytes();
                conn.setRequestProperty(HttpHeader.CONTENT_LENGTH, String.valueOf(b.length));
                conn.getOutputStream().write(b, 0, b.length);
                conn.getOutputStream().flush();
                conn.getOutputStream().close();
            }

            conn.connect();

            return getResult(conn);
        } catch (Exception e) {
            log.error("Exception while request: {}, caused : {}", url, e.getMessage(), e);
            return new HttpResult(500, e.toString(), Collections.emptyMap());
        } finally {
            IOUtils.close(conn);
        }
    }

    public static HttpResult httpPost(
            String url, List<String> headers, Map<String, String> paramValues) {

        List<NameValuePair> nvps = new ArrayList<>();
        for (Map.Entry<String, String> entry : paramValues.entrySet()) {
            nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }

        try {
            return httpPost(url, headers, new UrlEncodedFormEntity(nvps, UTF_8), UTF_8);
        } catch (UnsupportedEncodingException e) {
            return new HttpResult(500, e.toString(), Collections.emptyMap());
        }
    }

    public static HttpResult httpPost(String url, List<String> headers, String payload) {
        return httpPost(
                url, headers, new StringEntity(payload, ContentType.APPLICATION_JSON), UTF_8);
    }

    /**
     * Request http post method.
     *
     * @param url url
     * @param headers headers
     * @param requestEntity params
     * @param encoding charset
     * @return {@link HttpResult} as response
     */
    public static HttpResult httpPost(
            String url, List<String> headers, HttpEntity requestEntity, String encoding) {
        try {

            HttpPost httpost = new HttpPost(url);

            RequestConfig requestConfig =
                    RequestConfig.custom()
                            .setConnectionRequestTimeout(TIME_OUT_MILLIS)
                            .setConnectTimeout(CON_TIME_OUT_MILLIS)
                            .setSocketTimeout(SOC_TIME_OUT_MILLIS)
                            .setRedirectsEnabled(false)
                            .setCookieSpec(CookieSpecs.STANDARD)
                            .build();
            httpost.setConfig(requestConfig);

            if (null != headers) {
                for (Iterator<String> iter = headers.iterator(); iter.hasNext(); ) {
                    httpost.setHeader(iter.next(), iter.next());
                }
            }

            httpost.setEntity(requestEntity);
            HttpResponse response = postClient.execute(httpost);
            HttpEntity entity = response.getEntity();

            String charset = encoding;
            if (entity.getContentType() != null) {

                HeaderElement[] headerElements = entity.getContentType().getElements();

                if (headerElements != null
                        && headerElements.length > 0
                        && headerElements[0] != null
                        && headerElements[0].getParameterByName("charset") != null) {
                    charset = headerElements[0].getParameterByName("charset").getValue();
                }
            }

            return new HttpResult(
                    response.getStatusLine().getStatusCode(),
                    IOUtils.toString(entity.getContent(), charset),
                    Collections.emptyMap());
        } catch (Exception e) {
            if (e instanceof ConnectionPoolTimeoutException) {
                return new HttpResult(408, e.toString(), Collections.emptyMap());
            }
            return new HttpResult(500, e.toString(), Collections.emptyMap());
        }
    }

    private static HttpResult getResult(HttpURLConnection conn) throws IOException {
        int respCode = conn.getResponseCode();

        InputStream inputStream;
        if (HttpURLConnection.HTTP_OK == respCode) {
            inputStream = conn.getInputStream();
        } else {
            inputStream = conn.getErrorStream();
        }

        Map<String, String> respHeaders = new HashMap<>(conn.getHeaderFields().size());
        for (Map.Entry<String, List<String>> entry : conn.getHeaderFields().entrySet()) {
            respHeaders.put(entry.getKey(), entry.getValue().get(0));
        }

        if (HttpHeader.GZIP_VALUE.equals(respHeaders.get(HttpHeaders.CONTENT_ENCODING))) {
            inputStream = new GZIPInputStream(inputStream);
        }

        return new HttpResult(
                respCode, IOUtils.toString(inputStream, getCharset(conn)), respHeaders);
    }

    private static String getCharset(HttpURLConnection conn) {
        String contentType = conn.getContentType();
        if (StringUtils.isEmpty(contentType)) {
            return UTF_8;
        }

        String[] values = contentType.split(";");
        if (values.length == 0) {
            return UTF_8;
        }

        String charset = UTF_8;
        for (String value : values) {
            value = value.trim();

            if (value.toLowerCase().startsWith("charset=")) {
                charset = value.substring("charset=".length());
            }
        }

        return charset;
    }

    private static void setHeaders(HttpURLConnection conn, List<String> headers, String encoding) {
        if (null != headers) {
            for (Iterator<String> iter = headers.iterator(); iter.hasNext(); ) {
                conn.addRequestProperty(iter.next(), iter.next());
            }
        }

        conn.addRequestProperty(
                HttpHeader.CONTENT_TYPE,
                HttpHeader.APPLICATION_X_WWW_FORM_URLENCODED_VALUE + ";charset=" + encoding);
        conn.addRequestProperty(HttpHeader.ACCEPT_CHARSET, encoding);
    }

    /**
     * Encoding parameters.
     *
     * @param params parameters
     * @param encoding charset
     * @return parameters string
     * @throws UnsupportedEncodingException unsupported encoding exception
     */
    public static String encodingParams(Map<String, String> params, String encoding)
            throws UnsupportedEncodingException {
        StringBuilder sb = new StringBuilder();
        if (null == params || params.isEmpty()) {
            return null;
        }

        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (StringUtils.isEmpty(entry.getValue())) {
                continue;
            }

            sb.append(entry.getKey()).append("=");
            sb.append(URLEncoder.encode(entry.getValue(), encoding));
            sb.append("&");
        }

        return sb.toString();
    }

    /**
     * Translate parameter map.
     *
     * @param parameterMap parameter map
     * @return new parameter
     */
    public static Map<String, String> translateParameterMap(Map<String, String[]> parameterMap) {
        Map<String, String> map = new HashMap<>(16);
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            map.put(entry.getKey(), entry.getValue()[0]);
        }
        return map;
    }

    public static class HttpResult {

        public final int code;

        public final String content;

        private final Map<String, String> respHeaders;

        public HttpResult(int code, String content, Map<String, String> respHeaders) {
            this.code = code;
            this.content = content;
            this.respHeaders = respHeaders;
        }

        public String getHeader(String name) {
            return respHeaders.get(name);
        }

        public Map<String, String> getRespHeaders() {
            return this.respHeaders;
        }
    }
}
