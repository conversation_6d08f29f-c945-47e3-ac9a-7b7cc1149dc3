package point.common.model.request;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import point.common.util.DateUnit;
import point.common.util.StringUtil;

public abstract class AbstractUserInfoForm {

    @Getter @Setter protected String zipCode;

    @Getter @Setter protected String prefecture;

    @Getter @Setter protected String city;

    @Getter @Setter protected String address;

    @Getter @Setter protected String building;

    @Getter @Setter protected String foreignPeps;

    @Getter @Setter protected Boolean insider;

    protected boolean validateKatakana(String source) {
        if (StringUtils.isBlank(source)) {
            return true;
        }
        return source.matches("^[\\u30A0-\\u30FF 　]+$");
    }

    public boolean validateRange(String source, Integer min, Integer max, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return !StringUtils.isBlank(source)
                && (source.matches("[0-9]*"))
                && (min <= Integer.parseInt(source) && Integer.parseInt(source) <= max);
    }

    public boolean validateLength(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return !StringUtils.isBlank(source) && source.length() <= 255;
    }

    protected boolean validateBoolean(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return !StringUtils.isBlank(source)
                && (source.contains("false") || source.contains("true"));
    }

    public boolean validateYear(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return source.matches("[0-9]+") && source.length() == 4;
    }

    public boolean validateMonth(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return source.matches("[0-9]+") && Integer.parseInt(source) <= 12;
    }

    public boolean validateDate(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return source.matches("[0-9]+") && Integer.parseInt(source) <= 31;
    }

    public boolean validatePhoneNumber(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return StringUtil.validatePhoneNumber(source);
    }

    public boolean validatePersonalPhoneNumber(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return StringUtil.validatePersonalPhoneNumber(source);
    }

    public boolean validateBirthday(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return !StringUtils.isBlank(source) && source.matches("[0-9]*") && source.length() == 8;
    }

    public boolean validateGender(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }

        return !StringUtils.isBlank(source) && source.matches("[0-2]") && source.length() == 1;
    }

    public boolean validateZipCode(boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(zipCode)) {
            return true;
        }

        return !StringUtils.isBlank(zipCode) && zipCode.matches("[0-9]*") && zipCode.length() == 7;
    }

    public boolean validatePrefecture(boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(prefecture)) {
            return true;
        }

        return !StringUtils.isBlank(prefecture)
                && prefecture.matches("[0-9]*")
                && prefecture.length() == 2;
    }

    public boolean validateCity(boolean isEmptyOk) {
        return validateLength(city, isEmptyOk);
    }

    public boolean validateAddress(boolean isEmptyOk) {
        return validateLength(address, isEmptyOk);
    }

    public boolean validateBuilding(boolean isEmptyOk) {
        return validateLength(building, isEmptyOk);
    }

    public boolean validateForeignPeps(boolean isEmptyOk) {
        return validateBoolean(foreignPeps, isEmptyOk);
    }

    public boolean validateResidenceStatusLength(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }
        return !StringUtils.isBlank(source) && source.length() <= 12;
    }

    public boolean validateResidenceCardExpiredAt(String source, boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(source)) {
            return true;
        }
        Date residenceCardExpiredAt = DateUnit.toFormatStrToDate(source);
        Date currentDate = new Date();
        long differenceInDays =
                (residenceCardExpiredAt.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);
        return differenceInDays >= 90;
    }
}
