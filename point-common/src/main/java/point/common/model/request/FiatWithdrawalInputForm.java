package point.common.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class FiatWithdrawalInputForm {

    @Getter @Setter @NotNull private Long userId;

    @Getter @Setter @NotNull private BigDecimal amount;

    @Getter @Setter private BigDecimal fee;

    @Getter @Setter @NotNull private Long BankAccountId;

    @Getter @Setter @NotNull private String fiatWithdrawalStatus;

    @Getter @Setter private String comment;
}
