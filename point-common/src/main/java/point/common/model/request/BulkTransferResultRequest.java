package point.common.model.request;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/** BulkTransferResultRequest */
@Getter
@Setter
@Builder
public class BulkTransferResultRequest {
    private String accountId;

    private String applyNo;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class BulkTransferResultRequest {\n");

        sb.append("    accountId: ").append(toIndentedString(accountId)).append("\n");
        sb.append("    applyNo: ").append(toIndentedString(applyNo)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
