package point.common.model.request;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import point.common.constant.ReportLabel;

public class UserInfoForm extends AbstractUserInfoForm {

    @Getter @Setter private String firstName;

    @Getter @Setter private String lastName;

    @Getter @Setter private String firstKana;

    @Getter @Setter private String lastKana;

    @Getter @Setter private String nationality;

    @Getter @Setter private String birthday;

    @Getter @Setter private String gender;

    @Getter @Setter private String phoneNumber;

    @Getter @Setter private String occupation;

    @Getter @Setter private String industry;

    @Getter @Setter private String workPlace;

    @Getter @Setter private String position;

    @Getter @Setter private String priceFrom;

    @Getter @Setter private String income;

    @Getter @Setter private String financialAssets;

    @Getter @Setter private String purpose;

    @Getter @Setter private String investmentPurposes;

    @Getter @Setter private String cryptoExperience;

    @Getter @Setter private String fxExperience;

    @Getter @Setter private String stocksExperience;

    @Getter @Setter private String fundExperience;

    @Getter @Setter private String applicationHistory;

    @Getter @Setter private String applicationHistoryOther;

    @Getter @Setter private String country;

    @Getter @Setter private String residenceCardExpiredAt;

    @Getter @Setter private String residenceStatus;

    public boolean validateFirstName(boolean isEmptyOk) {
        return validateLength(firstName, isEmptyOk);
    }

    public boolean validateLastName(boolean isEmptyOk) {
        return validateLength(lastName, isEmptyOk);
    }

    public boolean validateFirstKana(boolean isEmptyOk) {
        return validateLength(firstKana, isEmptyOk) && validateKatakana(firstKana);
    }

    public boolean validateLastKana(boolean isEmptyOk) {
        return validateLength(lastKana, isEmptyOk) && validateKatakana(lastKana);
    }

    public boolean validateNationality(boolean isEmptyOk) {
        return validateLength(nationality, isEmptyOk);
    }

    public boolean validatePhoneNumber(boolean isEmptyOk) {
        return validatePhoneNumber(phoneNumber, isEmptyOk);
    }

    public boolean validatePersonalPhoneNumber(boolean isEmptyOk) {
        return validatePersonalPhoneNumber(phoneNumber, isEmptyOk);
    }

    public boolean validateWorkPlace(boolean isEmptyOk) {
        return validateLength(workPlace, isEmptyOk);
    }

    public boolean validatePosition(boolean isEmptyOk) {
        return validateLength(position, isEmptyOk);
    }

    /** 職業 専業主婦 / 主夫・学生・無職が選ばれた時に勤務地/部署・役職を空に良いか判定 */
    public boolean canEmptyOccupation(boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(occupation)) {
            return true;
        }

        return !StringUtils.isBlank(occupation)
                && occupation.matches("[0-9]*")
                && (9 <= Integer.parseInt(occupation) && Integer.parseInt(occupation) <= 11);
    }

    /** 職業 会社役員 / 団体役員・会社員 / 団体職員・個人事業主 / 自営業が選ばれた時に業種を空にすることはできませんか判定 */
    public boolean notEmptyOccupation(boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(occupation)) {
            return true;
        }

        return !StringUtils.isBlank(occupation)
                && occupation.matches("[0-9]*")
                && (ReportLabel.Occupation.OFFICER.getCode() != Integer.parseInt(occupation)
                        && ReportLabel.Occupation.WORKER.getCode() != Integer.parseInt(occupation)
                        && ReportLabel.Occupation.PROPRIETORSHIP.getCode()
                                != Integer.parseInt(occupation));
    }

    public boolean validateOccupation(boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(occupation)) {
            return true;
        }

        return !StringUtils.isBlank(occupation)
                && occupation.matches("[0-9]*")
                && Integer.parseInt(occupation) <= 11;
    }

    public boolean validateIndustry(boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(industry)) {
            return true;
        }

        return !StringUtils.isBlank(industry)
                && industry.matches("[0-9]*")
                && Integer.parseInt(industry) <= 15;
    }

    public boolean validatePriceFrom(boolean isEmptyOk) {
        if (isEmptyOk && StringUtils.isBlank(priceFrom)) {
            return true;
        }

        return !StringUtils.isBlank(priceFrom)
                && priceFrom.matches("[0-9]*")
                && Integer.parseInt(priceFrom) <= 8;
    }

    public boolean validateBirthday(boolean isEmptyOk) {
        return validateBirthday(birthday, isEmptyOk);
    }

    public boolean validateGender(boolean isEmptyOk) {
        return validateGender(gender, isEmptyOk);
    }

    public boolean validateIncome(boolean isEmptyOk) {
        return validateRange(income, 1, 8, isEmptyOk);
    }

    public boolean validateFinancialAssets(boolean isEmptyOk) {
        return validateRange(financialAssets, 1, 8, isEmptyOk);
    }

    public boolean validatePurpose(boolean isEmptyOk) {
        return validateRange(purpose, 1, 6, isEmptyOk);
    }

    public boolean validateInvestmentPurposes(boolean isEmptyOk) {
        return validateRange(investmentPurposes, 0, 4, isEmptyOk);
    }

    public boolean validateCryptoExperience(boolean isEmptyOk) {
        return validateRange(cryptoExperience, 0, 4, isEmptyOk);
    }

    public boolean validateFxExperience(boolean isEmptyOk) {
        return validateRange(fxExperience, 0, 4, isEmptyOk);
    }

    public boolean validateStocksExperience(boolean isEmptyOk) {
        return validateRange(stocksExperience, 0, 4, isEmptyOk);
    }

    public boolean validateFundExperience(boolean isEmptyOk) {
        return validateRange(fundExperience, 0, 4, isEmptyOk);
    }

    public boolean validateApplicationHistory(boolean isEmptyOk) {
        return validateRange(applicationHistory, 1, 7, isEmptyOk);
    }

    public boolean validateApplicationHistoryOther(boolean isEmptyOk) {
        return validateLength(applicationHistoryOther, isEmptyOk);
    }

    public boolean IsApplicationHistoryOther(boolean isEmptyOk) {
        if (!isEmptyOk) return applicationHistory.contains("7");
        return true;
    }

    public boolean validateCountry(boolean isEmptyOk) {
        return validateLength(country, isEmptyOk);
    }

    public boolean validateResidenceStatus(boolean isEmptyOk) {
        return validateResidenceStatusLength(residenceStatus, isEmptyOk);
    }

    public boolean validateResidenceCardExpiredAt(boolean isEmptyOk) {
        return validateResidenceCardExpiredAt(residenceCardExpiredAt, isEmptyOk);
    }
}
