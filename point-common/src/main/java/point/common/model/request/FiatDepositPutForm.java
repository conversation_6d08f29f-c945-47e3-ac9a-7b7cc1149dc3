package point.common.model.request;

import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

public class FiatDepositPutForm {
    @Getter @Setter private Long userId;

    @Getter @Setter private Long id;

    @Getter @Setter private Long bankAccountId;

    @Getter @Setter private BigDecimal amount;

    @Getter @Setter private BigDecimal fee;

    @Getter @Setter private String fiatDepositStatus;

    @Getter @Setter private String fiatDepositSubStatus;

    @Getter @Setter private String comment;
}
