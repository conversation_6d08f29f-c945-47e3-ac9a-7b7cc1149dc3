package point.common.model.request;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class VaIssueRequest {
    private String vaTypeCode;

    private String issueRequestCount;

    private String raId;

    private String vaContractAuthKey;

    private String vaHolderNameKana;

    private String vaHolderNamePos;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class VaIssueRequest {\n");

        sb.append("    vaTypeCode: ").append(toIndentedString(vaTypeCode)).append("\n");
        sb.append("    issueRequestCount: ")
                .append(toIndentedString(issueRequestCount))
                .append("\n");
        sb.append("    raId: ").append(toIndentedString(raId)).append("\n");
        sb.append("    vaContractAuthKey: ")
                .append(toIndentedString(vaContractAuthKey))
                .append("\n");
        sb.append("    vaHolderNameKana: ").append(toIndentedString(vaHolderNameKana)).append("\n");
        sb.append("    vaHolderNamePos: ").append(toIndentedString(vaHolderNamePos)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
