package point.common.model.sqs;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

public class MessageBodyData implements Serializable {

    private static class MessageAttributes implements Serializable {

        private static class MessageAttribute implements Serializable {

            private static final long serialVersionUID = 4810015914288916624L;

            @Getter
            @Setter
            @JsonProperty("Type")
            private String type;

            @Getter
            @Setter
            @JsonProperty("Value")
            private String value;
        }

        private static final long serialVersionUID = -91364838445352111L;

        @Getter
        @Setter
        @JsonProperty("AWS.SNS.MOBILE.MPNS.Type")
        private MessageAttribute mpnsType;

        @Getter
        @Setter
        @JsonProperty("AWS.SNS.MOBILE.MPNS.NotificationClass")
        private MessageAttribute mpnsNotificationClass;

        @Getter
        @Setter
        @JsonProperty("AWS.SNS.MOBILE.WNS.Type")
        private MessageAttribute wnsType;
    }

    private static final long serialVersionUID = -2935635884748965133L;

    @Getter
    @Setter
    @JsonProperty("Type")
    private String type;

    @Getter
    @Setter
    @JsonProperty("MessageId")
    private String messageId;

    @Getter
    @Setter
    @JsonProperty("TopicArn")
    private String topicArn;

    @Getter
    @Setter
    @JsonProperty("Subject")
    private String subject;

    @Getter
    @Setter
    @JsonProperty("Message")
    private String message;

    @Getter
    @Setter
    @JsonProperty("Timestamp")
    private String timestamp;

    @Getter
    @Setter
    @JsonProperty("SignatureVersion")
    private String signatureVersion;

    @Getter
    @Setter
    @JsonProperty("Signature")
    private String signature;

    @Getter
    @Setter
    @JsonProperty("SigningCertURL")
    private String signingCertUrl;

    @Getter
    @Setter
    @JsonProperty("UnsubscribeURL")
    private String unsubscribeUrl;

    @Getter
    @Setter
    @JsonProperty("MessageAttributes")
    private MessageAttributes messageAttributes;
}
