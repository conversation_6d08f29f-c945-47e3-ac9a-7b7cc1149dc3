package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ChoiceActivityResultStatus;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChoiceInfoData implements Serializable {

    private Date voteStartTime;

    private Date voteEndTime;

    private Date lockStartTime;

    private Date lockEndTime;

    private Date basePriceDate;

    private BigDecimal basePrice;

    private Long rewardPool;

    private Long userPower;

    private BigDecimal upVoteRate;

    private BigDecimal downVoteRate;

    private ChoiceActivityResultStatus status;

    private boolean buttonFlag;

    private boolean editFlag;

    private Long choiceVoteId;

    private Long choiceUserVotePower;

    private Long activityId;

    // private BigDecimal rewardChoicePAmount;

    private BigDecimal withdrawChoicePAmount;

    private Long allUserVoteCount;

    private String voteCategory;

    private Long investUserId;
    private Long operateUserId;

    private Long totalVotePowerDown;
    private Long totalVotePowerUp;

    public BigDecimal getWithdrawChoicePAmount() {
        return Objects.isNull(this.withdrawChoicePAmount)
                ? BigDecimal.ZERO
                : this.withdrawChoicePAmount;
    }
}
