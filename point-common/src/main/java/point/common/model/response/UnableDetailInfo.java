package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** UnableDetailInfo */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class UnableDetailInfo {
    @JsonAlias("transferDetailStatus")
    private String transferDetailStatus = null;

    @JsonAlias("refundStatus")
    private String refundStatus = null;

    @JsonAlias("isRepayment")
    private Boolean isRepayment = null;

    @JsonAlias("repaymentDate")
    private String repaymentDate = null;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class UnableDetailInfo {\n");

        sb.append("    transferDetailStatus: ")
                .append(toIndentedString(transferDetailStatus))
                .append("\n");
        sb.append("    refundStatus: ").append(toIndentedString(refundStatus)).append("\n");
        sb.append("    isRepayment: ").append(toIndentedString(isRepayment)).append("\n");
        sb.append("    repaymentDate: ").append(toIndentedString(repaymentDate)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
