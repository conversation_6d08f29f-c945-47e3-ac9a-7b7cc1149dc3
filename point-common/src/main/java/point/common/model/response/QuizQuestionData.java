package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.CommonConstants;
import point.common.entity.QuizQuestion;
import point.common.entity.QuizQuestionPublishedRecord;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class QuizQuestionData implements Serializable {

    private static final long serialVersionUID = -5680701498263865050L;

    private Long id;

    private String quizNum;

    private String title;

    private String optionA;

    private String optionB;

    private String optionC;

    private String optionD;

    private String userAnswer;

    private String correctAnswer;

    private String correctAnswerContent;

    private String optionAWrongReason;
    private String optionBWrongReason;
    private String optionCWrongReason;
    private String optionDWrongReason;

    public QuizQuestionData(
            QuizQuestionPublishedRecord quizQuestionPublishedRecord, String userAnswer) {
        this.id = quizQuestionPublishedRecord.getQuizQuestion().getId();
        this.quizNum = quizQuestionPublishedRecord.getQuizQuestion().getQuizNum();
        this.title = quizQuestionPublishedRecord.getQuizQuestion().getTitle();
        this.optionA = quizQuestionPublishedRecord.getQuizQuestion().getOptionA();
        this.optionB = quizQuestionPublishedRecord.getQuizQuestion().getOptionB();
        this.optionC = quizQuestionPublishedRecord.getQuizQuestion().getOptionC();
        this.optionD = quizQuestionPublishedRecord.getQuizQuestion().getOptionD();
        this.correctAnswer = quizQuestionPublishedRecord.getQuizQuestion().getAnswer();
        this.userAnswer = userAnswer;
        this.correctAnswerContent =
                quizQuestionPublishedRecord.getQuizQuestion().getCorrectAnswerContent();
        this.optionAWrongReason =
                quizQuestionPublishedRecord.getQuizQuestion().getOptionAWrongReason();
        this.optionBWrongReason =
                quizQuestionPublishedRecord.getQuizQuestion().getOptionBWrongReason();
        this.optionCWrongReason =
                quizQuestionPublishedRecord.getQuizQuestion().getOptionCWrongReason();
        this.optionDWrongReason =
                quizQuestionPublishedRecord.getQuizQuestion().getOptionDWrongReason();
    }

    public QuizQuestionData(QuizQuestionPublishedRecord quizQuestionPublishedRecord) {
        this.id = quizQuestionPublishedRecord.getQuizQuestion().getId();
        this.quizNum = quizQuestionPublishedRecord.getQuizQuestion().getQuizNum();
        this.title = quizQuestionPublishedRecord.getQuizQuestion().getTitle();
        this.optionA = quizQuestionPublishedRecord.getQuizQuestion().getOptionA();
        this.optionB = quizQuestionPublishedRecord.getQuizQuestion().getOptionB();
        this.optionC = quizQuestionPublishedRecord.getQuizQuestion().getOptionC();
        this.optionD = quizQuestionPublishedRecord.getQuizQuestion().getOptionD();
    }

    public static QuizQuestionData convertToRandomizedDto(
            QuizQuestionPublishedRecord publishedRecord) {
        Map<String, String> mapping = parseMapping(publishedRecord.getAnswerMapping());
        QuizQuestion question = publishedRecord.getQuizQuestion();
        QuizQuestionData dto = new QuizQuestionData();
        dto.setId(question.getId());
        dto.setQuizNum(question.getQuizNum());
        dto.setTitle(question.getTitle());

        String randomAnswer = mapping.get(question.getAnswer());
        dto.setCorrectAnswer(randomAnswer);
        dto.setCorrectAnswerContent(question.getCorrectAnswerContent());

        // Create answer option mapping
        Map<String, OptionContent> optionMapping = createOptionMapping(question, mapping);

        dto.setOptionA(optionMapping.get("A").content);
        dto.setOptionB(optionMapping.get("B").content);
        dto.setOptionC(optionMapping.get("C").content);
        dto.setOptionD(optionMapping.get("D").content);

        dto.setOptionAWrongReason(optionMapping.get("A").wrongReason);
        dto.setOptionBWrongReason(optionMapping.get("B").wrongReason);
        dto.setOptionCWrongReason(optionMapping.get("C").wrongReason);
        dto.setOptionDWrongReason(optionMapping.get("D").wrongReason);

        return dto;
    }

    private static Map<String, OptionContent> createOptionMapping(
            QuizQuestion question, Map<String, String> mapping) {
        Map<String, OptionContent> result = new HashMap<>();

        Map<String, String> reverseMapping = new HashMap<>();
        mapping.forEach((originalKey, displayKey) -> reverseMapping.put(displayKey, originalKey));

        for (String displayKey : Arrays.asList("A", "B", "C", "D")) {
            String originalKey = reverseMapping.getOrDefault(displayKey, displayKey);
            result.put(
                    displayKey,
                    new OptionContent(
                            getOptionContent(question, originalKey),
                            getWrongReason(question, originalKey)));
        }

        return result;
    }

    private static class OptionContent {
        String content;
        String wrongReason;

        OptionContent(String content, String wrongReason) {
            this.content = content;
            this.wrongReason = wrongReason;
        }
    }

    private static String getOptionContent(QuizQuestion question, String originalKey) {
        switch (originalKey) {
            case "A":
                return question.getOptionA();
            case "B":
                return question.getOptionB();
            case "C":
                return question.getOptionC();
            case "D":
                return question.getOptionD();
            default:
                throw new IllegalArgumentException("Invalid option key");
        }
    }

    private static String getWrongReason(QuizQuestion question, String originalKey) {
        switch (originalKey) {
            case "A":
                return question.getOptionAWrongReason();
            case "B":
                return question.getOptionBWrongReason();
            case "C":
                return question.getOptionCWrongReason();
            case "D":
                return question.getOptionDWrongReason();
            default:
                throw new IllegalArgumentException("Invalid option key");
        }
    }

    public static QuizQuestionData convertToRandomizedDto(
            QuizQuestionPublishedRecord quizQuestionPublishedRecord, String userAnswer) {
        QuizQuestionData quizQuestionData = convertToRandomizedDto(quizQuestionPublishedRecord);
        quizQuestionData.setUserAnswer(userAnswer);
        return quizQuestionData;
    }

    private static Map<String, String> parseMapping(String answerMapping) {
        return Arrays.stream(answerMapping.split(CommonConstants.COMMA))
                .map(pair -> pair.split(CommonConstants.COLON))
                .collect(Collectors.toMap(parts -> parts[0], parts -> parts[1]));
    }
}
