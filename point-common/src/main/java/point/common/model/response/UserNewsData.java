package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class UserNewsData implements Serializable {

    private static final long serialVersionUID = 332350155679199387L;

    @Getter @Setter private Long userId;

    @Getter @Setter private Long[] readIds;
}
