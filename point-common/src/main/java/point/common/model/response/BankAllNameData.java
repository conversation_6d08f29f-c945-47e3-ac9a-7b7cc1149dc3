package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BankAllNameData implements Serializable {

    private static final long serialVersionUID = 332350155679199387L;

    @Getter @Setter private Integer bankCode;

    @Getter @Setter private String bankName;
}
