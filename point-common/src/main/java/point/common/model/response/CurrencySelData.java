package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Currency;
import point.common.constant.TradeType;
import point.common.entity.CurrencyConfig;
import point.common.serializer.BigDecimalSerializer;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CurrencySelData implements Serializable {

    private static final long serialVersionUID = 9071421502785957170L;

    @Getter @Setter private Long id;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private Currency currency;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal depositFee = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal withdrawalFee = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal transactionFee = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal maxOrderAmountPerDay = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal minDepositAmount = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Getter
    @Setter
    private BigDecimal minWithdrawalAmount = BigDecimal.ZERO;

    @Getter @Setter private boolean depositable = true;

    @Getter @Setter private boolean withdrawable = true;

    @Getter @Setter private boolean enabled = false;

    @Getter @Setter private String labelNm;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    public CurrencySelData setProperties(CurrencyConfig currencyConfig) {
        this.id = currencyConfig.getId();
        this.currency = currencyConfig.getCurrency();
        this.depositFee = currencyConfig.getDepositFee();
        this.withdrawalFee = currencyConfig.getWithdrawalFee();
        this.transactionFee = currencyConfig.getTransactionFee();
        this.maxOrderAmountPerDay = currencyConfig.getMaxOrderAmountPerDay();
        this.minDepositAmount = currencyConfig.getMinDepositAmount();
        this.minWithdrawalAmount = currencyConfig.getMinWithdrawalAmount();
        this.depositable = currencyConfig.isDepositable();
        this.withdrawable = currencyConfig.isWithdrawable();
        this.enabled = currencyConfig.isEnabled();
        this.labelNm = currencyConfig.getCurrency().getLabel();
        this.setTradeType(currencyConfig.getTradeType());
        return this;
    }
}
