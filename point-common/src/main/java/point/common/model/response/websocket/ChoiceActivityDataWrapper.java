package point.common.model.response.websocket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ChoiceActivityVoteAction;
import point.common.entity.ChoiceVote;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChoiceActivityDataWrapper extends DataWrapper implements Serializable {

    @Serial private static final long serialVersionUID = -986736058848090554L;

    private ChoiceVote choiceVote;
    private ChoiceActivityVoteAction voteAction;
    private Long balanceVote;

    @Override
    public String getChecksum() {
        return calculateChecksum(this.getCacheKey());
    }

    @Override
    public String getCacheKey() {
        return "choice:activity:vote:"
                + choiceVote.getActivityId()
                + ":"
                + choiceVote.getId()
                + ":"
                + choiceVote.getVotePower()
                + ":"
                + choiceVote.getVoteDirection().name()
                + ":"
                + voteAction.name();
    }
}
