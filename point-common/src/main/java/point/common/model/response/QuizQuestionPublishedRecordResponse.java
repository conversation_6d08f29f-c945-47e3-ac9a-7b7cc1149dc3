package point.common.model.response;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import point.common.entity.QuizQuestionPublishedRecord;

@Getter
@Setter
public class QuizQuestionPublishedRecordResponse {

    private Long id;

    private Date publishedDate;

    private QuizQuestionData quizQuestion;

    private Long quizQuestionId;

    private String randomAnswer;

    private String answerMapping;

    public QuizQuestionPublishedRecordResponse(
            QuizQuestionPublishedRecord record, QuizQuestionData quizQuestion) {
        this.id = record.getId();
        this.publishedDate = record.getPublishedDate();
        this.quizQuestion = quizQuestion;
        this.quizQuestionId = record.getQuizQuestionId();
        this.randomAnswer = record.getRandomAnswer();
        this.answerMapping = record.getAnswerMapping();
    }
}
