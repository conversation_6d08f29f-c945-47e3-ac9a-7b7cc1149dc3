package point.pos.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.*;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class PosBestPriceData implements Serializable {

    private static final long serialVersionUID = 6602255404195644180L;

    public static class PosTicker {

        @Getter @Setter private BigDecimal open = BigDecimal.ZERO;

        @Getter @Setter private BigDecimal high = BigDecimal.ZERO;

        @Getter @Setter private BigDecimal low = BigDecimal.ZERO;

        @Getter @Setter private BigDecimal last = BigDecimal.ZERO;

        @Getter @Setter private BigDecimal volume = BigDecimal.ZERO;
    }

    private BigDecimal bestBid;

    private BigDecimal bestAsk;

    private BigDecimal bestMmBid;

    private BigDecimal bestMmAsk;

    private BigDecimal mid;

    private Long symbolId;
    @Getter @Setter private BigDecimal open;

    @Getter @Setter private BigDecimal high;

    @Getter @Setter private BigDecimal low;

    @Getter @Setter private BigDecimal last;

    @Getter @Setter private String volume;

    @Getter @Setter private long timestamp;

    private BigDecimal close;

    public static PosBestPriceData empty() {
        return PosBestPriceData.builder().build();
    }
}
