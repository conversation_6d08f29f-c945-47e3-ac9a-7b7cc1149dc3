package point.pos.predicate;

import java.math.BigDecimal;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.*;
import point.common.predicate.OrderPredicate;
import point.pos.entity.PosOrder;
import point.pos.entity.PosOrder_;

@Component
public class PosOrderPredicate extends OrderPredicate<PosOrder> {

    public Predicate equalOrderCovered(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, <PERSON><PERSON><PERSON> covered) {
        return criteriaBuilder.equal(root.get(PosOrder_.covered), covered);
    }

    public Predicate inSymbolIds(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, List<Long> symbolIds) {
        return root.get(PosOrder_.symbolId).in(symbolIds);
    }

    public Predicate equalOrderStatus(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, PosOrderStatus orderStatus) {
        return criteriaBuilder.equal(root.get(PosOrder_.orderStatus), orderStatus);
    }

    public Predicate inUserIds(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, List<Long> userIds) {
        return root.get(PosOrder_.userId).in(userIds);
    }

    public Predicate inExceptUserIds(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, List<Long> exceptUserIds) {
        return root.get(PosOrder_.userId).in(exceptUserIds).not();
    }

    public Predicate inOrderStatus(
            CriteriaBuilder criteriaBuilder,
            Root<PosOrder> root,
            List<PosOrderStatus> orderStatus) {
        return root.get(PosOrder_.orderStatus).in(orderStatus.toArray());
    }

    public Predicate equalOrderSide(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, OrderSide orderSide) {
        return criteriaBuilder.equal(root.get(PosOrder_.orderSide), orderSide);
    }

    public Predicate equalOrderChannel(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, OrderChannel orderChannel) {
        return criteriaBuilder.equal(root.get(PosOrder_.orderChannel), orderChannel);
    }

    public Predicate equalOrderType(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, OrderType orderType) {
        return criteriaBuilder.equal(root.get(PosOrder_.orderType), orderType);
    }

    public Predicate inOrderType(Root<PosOrder> root, OrderType... orderTypes) {
        return root.get(PosOrder_.orderType).in((Object[]) orderTypes);
    }

    public Predicate notInOrderType(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, OrderType... orderTypes) {
        return criteriaBuilder.not(inOrderType(root, orderTypes));
    }

    public Predicate inOrderStatus(Root<PosOrder> root, PosOrderStatus... orderStatuses) {
        return root.get(PosOrder_.orderStatus).in((Object[]) orderStatuses);
    }

    public Predicate betweenPrice(
            CriteriaBuilder criteriaBuilder,
            Root<PosOrder> root,
            BigDecimal priceFrom,
            BigDecimal priceTo) {
        return criteriaBuilder.between(root.get(PosOrder_.price), priceFrom, priceTo);
    }

    public Predicate inIds(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, List<Long> orderIds) {
        return root.get(PosOrder_.id).in(orderIds);
    }

    public Predicate equalIdType(
            CriteriaBuilder criteriaBuilder, Root<PosOrder> root, UserIdType userIdType) {
        return criteriaBuilder.equal(root.get(PosOrder_.idType), userIdType);
    }
}
