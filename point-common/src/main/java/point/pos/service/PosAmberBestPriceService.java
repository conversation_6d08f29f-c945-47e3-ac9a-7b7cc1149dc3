package point.pos.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.HmacUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import point.common.constant.CurrencyPair;
import point.common.entity.Symbol;
import point.common.http.cb.HttpClient;
import point.common.http.whalefin.WhaleFinClient;
import point.common.model.response.CurrencyConfigData;
import point.common.util.JsonUtil;
import point.pos.model.PosBestPriceData;

@Slf4j
@Transactional(readOnly = true, transactionManager = "masterTransactionManager")
@RequiredArgsConstructor
public class PosAmberBestPriceService {

    @Value("${point-pos.best-price.amber.api-host}")
    private String apiHost;

    @Value("${point-pos.best-price.amber.api-path}")
    private String apiPath;

    @Value("${point-pos.best-price.amber.access-secret}")
    private String accessSecret;

    @Value("${point-pos.best-price.amber.access-key}")
    private String accessKey;

    private static final String SIGN_ALGORITHM = "HmacSha256";
    private static final String GET_PRICE_METHOD = "GET";
    private static final String DIRECTION_SELL = "SELL";
    private static final String DIRECTION_BUY = "BUY";
    private static final int TOO_MANY_REQUESTS = 429;
    private static final String JPY = "_JPY";

    public PosBestPriceData getBestPrice(
            Symbol symbol, BigDecimal quantity, List<CurrencyConfigData> currencyConfigs) {
        if (isSpecialCurrencyPair(symbol)) {
            if (CollectionUtils.isEmpty(currencyConfigs)) {
                log.warn("No CurrencyConfigData found for symbol: {}", symbol.getId());
                return null;
            }
            return getBestPriceForSpecialCurrency(symbol, quantity, currencyConfigs);
        } else {
            return getBestPriceForRegularCurrency(symbol, quantity);
        }
    }

    private boolean isSpecialCurrencyPair(Symbol symbol) {
        return symbol.getCurrencyPair().equals(CurrencyPair.BALC_JPY)
                || symbol.getCurrencyPair().equals(CurrencyPair.ACTC_JPY);
    }

    private PosBestPriceData getBestPriceForSpecialCurrency(
            Symbol symbol, BigDecimal quantity, List<CurrencyConfigData> currencyConfigs) {
        List<CompletableFuture<PriceResult>> futures =
                currencyConfigs.stream()
                        .map(
                                config ->
                                        CompletableFuture.supplyAsync(
                                                () -> getPriceForConfig(symbol, quantity, config)))
                        .toList();
        CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        CompletableFuture<PosBestPriceData> resultFuture =
                allFutures.thenApply(
                        v -> {
                            BigDecimal totalAsk = BigDecimal.ZERO;
                            BigDecimal totalBid = BigDecimal.ZERO;
                            for (CompletableFuture<PriceResult> future : futures) {
                                try {
                                    PriceResult result = future.get();
                                    if (result != null) {
                                        totalAsk = totalAsk.add(result.bestAsk());
                                        totalBid = totalBid.add(result.bestBid());
                                    } else {
                                        return null;
                                    }
                                } catch (InterruptedException | ExecutionException e) {
                                    log.warn(
                                            "Failed to get price for special currency pair: {}",
                                            e.getMessage());
                                    return null;
                                }
                            }
                            return buildPosBestPriceData(symbol, totalAsk, totalBid);
                        });
        try {
            return resultFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            log.warn("Failed to process prices: {}", e.getMessage());
            return null;
        }
    }

    private PosBestPriceData getBestPriceForRegularCurrency(Symbol symbol, BigDecimal quantity) {

        quantity =
                symbol.getCurrencyPair()
                        .getBaseCurrency()
                        .getScaledAmount(quantity, RoundingMode.DOWN);

        BigDecimal bestAsk = getPrice(quantity, DIRECTION_BUY, symbol.getCurrencyPair().getName());

        BigDecimal bestBid = getPrice(quantity, DIRECTION_SELL, symbol.getCurrencyPair().getName());
        return buildPosBestPriceData(symbol, bestAsk, bestBid);
    }

    private PriceResult getPriceForConfig(
            Symbol symbol, BigDecimal quantity, CurrencyConfigData config) {
        String currencyPair = config.getCurrency() + JPY;
        BigDecimal scaledQuantity =
                symbol.getCurrencyPair()
                        .getBaseCurrency()
                        .getScaledAmount(quantity, RoundingMode.DOWN);

        BigDecimal bestAsk = getPrice(scaledQuantity, DIRECTION_BUY, currencyPair);

        BigDecimal bestBid = getPrice(scaledQuantity, DIRECTION_SELL, currencyPair);

        if (bestAsk == null || bestBid == null) {
            return null;
        }
        BigDecimal percentage = config.getPercentage().divide(new BigDecimal(100));
        bestAsk = bestAsk.multiply(percentage);
        bestBid = bestBid.multiply(percentage);

        return new PriceResult(bestAsk, bestBid);
    }

    private PosBestPriceData buildPosBestPriceData(
            Symbol symbol, BigDecimal bestAsk, BigDecimal bestBid) {
        return PosBestPriceData.builder()
                .symbolId(symbol.getId())
                .timestamp(System.currentTimeMillis())
                .bestAsk(
                        Objects.isNull(bestAsk) || bestAsk.equals(BigDecimal.ZERO) ? null : bestAsk)
                .bestBid(
                        Objects.isNull(bestBid) || bestBid.equals(BigDecimal.ZERO) ? null : bestBid)
                .build();
    }

    private record PriceResult(BigDecimal bestAsk, BigDecimal bestBid) {}

    public BigDecimal getPrice(BigDecimal quantity, String direction, String currencyPair) {
        long timestamp = System.currentTimeMillis();
        String params =
                "quantity="
                        + quantity.toString()
                        + "&direction="
                        + direction
                        + "&symbol="
                        + currencyPair;
        String path = apiPath + "?" + params;
        String signStr = "method=" + GET_PRICE_METHOD + "&path=" + path + "&timestamp=" + timestamp;

        String sign = new HmacUtils(SIGN_ALGORITHM, accessSecret).hmacHex(signStr);

        ArrayList<String> headers = new ArrayList<>(6);
        headers.add("access-key");
        headers.add(accessKey);
        headers.add("access-timestamp");
        headers.add(String.valueOf(timestamp));
        headers.add("access-sign");
        headers.add(sign);
        log.info(
                "pos amber best price,currency:{} access-key: {} , accessSecret: {}, access-timestamp: {}, sign: {}",
                currencyPair,
                accessKey,
                accessSecret,
                timestamp,
                sign);
        String requestUrl = apiHost + path;
        log.info("pos amber request-url:{}", requestUrl);
        HttpClient.HttpResult result =
                HttpClient.httpGet(requestUrl, headers, Collections.emptyMap());
        log.info(
                "pos amber repo: content: {}, code: {}, x-gw-traceid={}, x-gw-requestid={}",
                result.content,
                result.code,
                result.getHeader(WhaleFinClient.X_GW_TRACE_ID),
                result.getHeader(WhaleFinClient.X_GW_REQUEST_ID));
        if (HttpURLConnection.HTTP_BAD_GATEWAY == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {}, Bad Gateway",
                    requestUrl,
                    result.code,
                    result.content);
            return null;
        }
        if (HttpURLConnection.HTTP_INTERNAL_ERROR == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Internal Server Error",
                    requestUrl,
                    result.code,
                    result.content);
            return null;
        }
        if (HttpURLConnection.HTTP_BAD_REQUEST == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Bad request/Request failed",
                    requestUrl,
                    result.code,
                    result.content);
            return null;
        }
        if (HttpURLConnection.HTTP_UNAUTHORIZED == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Unauthorize",
                    requestUrl,
                    result.code,
                    result.content);
            return null;
        }
        if (HttpURLConnection.HTTP_FORBIDDEN == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Forbidden/IP restriction",
                    requestUrl,
                    result.code,
                    result.content);
            return null;
        }
        if (HttpURLConnection.HTTP_NOT_FOUND == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Not found",
                    requestUrl,
                    result.code,
                    result.content);
            return null;
        }
        if (HttpURLConnection.HTTP_UNSUPPORTED_TYPE == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Unsupported media type",
                    requestUrl,
                    result.code,
                    result.content);
            return null;
        }
        if (TOO_MANY_REQUESTS == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Too many requests",
                    requestUrl,
                    result.code,
                    result.content);
            return null;
        }
        String responseJson = result.content;
        Map<String, Object> responseObj = JsonUtil.decode(responseJson, Map.class);
        if (CollectionUtils.isEmpty(responseObj)) {
            log.warn("Failed to parse json for get best price of the amber: {}", responseJson);
            return null;
        }
        Object resultObj = responseObj.get("result");
        if (Objects.isNull(resultObj)) {
            log.warn("No price returned from amber: {}", responseJson);
            return null;
        }

        if (resultObj instanceof Map priceObj) {
            Map priceValue = (Map) priceObj.get("fiatReference");
            if (Objects.isNull(priceValue)) {
                log.warn("No price returned from amber: {}", responseJson);
                return null;
            }
            Object priceJyp = priceValue.get("jpy");
            if (Objects.isNull(priceJyp)) {
                log.warn("No priceJyp returned from amber: {}", responseJson);
                return null;
            }
            return new BigDecimal(priceJyp.toString());
        } else {
            log.warn("The result object does not expected: {}", responseJson);
            return null;
        }
    }
}
