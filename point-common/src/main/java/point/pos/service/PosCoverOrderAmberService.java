package point.pos.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.util.*;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import point.common.component.CustomTransactionManager;
import point.common.component.RedisManager;
import point.common.component.RedisManager.LockParams;
import point.common.component.SesManager;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.http.cb.HttpClient;
import point.common.service.*;
import point.common.util.FormatUtil;
import point.common.util.JsonUtil;
import point.pos.entity.PosCoverOrder;
import point.pos.entity.PosOrder;
import point.pos.entity.PosTrade;
import point.pos.predicate.PosCoverOrderPredicate;

@Slf4j
@RequiredArgsConstructor
public class PosCoverOrderAmberService
        extends EntityService<PosCoverOrder, PosCoverOrderPredicate> {
    @Value("${point-pos.base-trade.amber.api-host}")
    private String apiHost;

    @Value("${point-pos.base-trade.amber.api-path}")
    private String apiPath;

    @Value("${point-pos.base-trade.amber.access-key}")
    private String accessKey;

    @Value("${point-pos.base-trade.amber.access-secret}")
    private String accessSecret;

    private static final String SIGN_ALGORITHM = "HmacSha256";

    private static final String STRATEGY = "IOC";

    private static final String POS_TYPE = "LIMIT";

    private static final String GET_PRICE_METHOD = "POST";

    private final PosOrderService posOrderService;

    private final CurrencyPairConfigService currencyPairConfigService;

    private final CustomTransactionManager customTransactionManager;

    private final PosTradeService posTradeService;

    private final AssetService assetService;

    private final String COMPLETED = "COMPLETED";

    private final String PART_CANCELED = "PART_CANCELED";

    private static final int TOO_MANY_REQUESTS = 429;

    private final OrderbookService orderbookService;

    @Autowired private MailNoreplyService mailNoreplyService;

    @Autowired private SesManager sesManager;

    @Autowired private UserService userService;

    @Autowired private RedisManager redisManager;

    private static final int SCALE_3 = 3;

    @Autowired private ChoicePowerSyncService choicePowerSyncService;
    @Autowired private UserMonsterInfoService userMonsterInfoService;
    private final MonsterExperienceService monsterExperienceService;
    private final UserCropStatusService userCropStatusService;

    private static String getLockKey(Long userId, Currency currency) {
        return "lock:asset:" + userId + ":" + currency;
    }

    @Override
    public Class<PosCoverOrder> getEntityClass() {
        return PosCoverOrder.class;
    }

    public void executeTrade(Symbol symbol) throws Exception {
        log.info("executeTrade trade symbol start");
        List<PosOrder> posOrders = posOrderService.findByCondition(symbol.getId());
        log.info("executeTrade trade symbol posOrders size:{}", posOrders.size());
        for (PosOrder posOrder : posOrders) {
            try {
                log.info("posCoverOrderLog,symbolId," + symbol.getId() + ",pos_order_loop_start");
                User user = userService.findOne(posOrder.getUserId());
                CurrencyPairConfig currencyPairConfig =
                        currencyPairConfigService.findByCondition(
                                TradeType.INVEST, symbol.getCurrencyPair());
                BigDecimal posSlippagePercent =
                        currencyPairConfig
                                .getPosSlippagePercent()
                                .divide(new BigDecimal("100"), RoundingMode.DOWN);

                BigDecimal amount =
                        symbol.getCurrencyPair().getPosScaledAmount(posOrder.getAmount());
                BigDecimal scaledPrice =
                        symbol.getCurrencyPair().getScaledPrice(posOrder.getMmPrice());
                BigDecimal sendPrice;
                if (OrderSide.BUY.equals(posOrder.getOrderSide())) {
                    sendPrice = scaledPrice.add(posSlippagePercent.multiply(scaledPrice));
                } else {
                    sendPrice = scaledPrice.subtract(posSlippagePercent.multiply(scaledPrice));
                }
                String clientId =
                        TradeType.INVEST.getName()
                                + ":"
                                + posOrder.getId()
                                + ":"
                                + System.currentTimeMillis();
                String direction = posOrder.getOrderSide().getName();
                String amberSymbol = symbol.getCurrencyPairName();
                String price = symbol.getCurrencyPair().getScaledPrice(sendPrice).toString();
                RequestOrder createPosOrder =
                        new RequestOrder(
                                direction,
                                price,
                                STRATEGY,
                                POS_TYPE,
                                clientId,
                                amberSymbol,
                                String.valueOf(amount));
                String payload = JsonUtil.encode(createPosOrder);
                Result result = getRequestTradeOrdersPos(payload, posOrder, symbol, user);
                if (!ObjectUtils.isEmpty(result)) {
                    createPosTrace(result, symbol, posOrder, user);
                }
            } catch (Exception e) {
                log.warn("request amber error callback");
                assetUpdate(symbol, posOrder);
                sendCustomerCancelMail(
                        posOrder, mailNoreplyService, sesManager, symbol, new User());
            }
        }
        log.info("Sending order");
    }

    private void createPosTrace(Result result, Symbol symbol, PosOrder posOrder, User user)
            throws Exception {
        if (!redisManager.executeWithLock(
                getLockKey(posOrder.getUserId(), symbol.getCurrencyPair().getQuoteCurrency()),
                LockParams.EXECUTE_ORDER,
                () -> {
                    customTransactionManager.execute(
                            entityManager -> {
                                BigDecimal posOrderPrice = posOrder.getPrice();
                                BigDecimal quantityAmount = new BigDecimal(result.quantity);
                                BigDecimal filledQuantityAmount =
                                        new BigDecimal(result.filledQuantity);
                                BigDecimal remainingAmount =
                                        quantityAmount.subtract(filledQuantityAmount);
                                BigDecimal assetAmountScaled =
                                        calculateAssetAmount(
                                                symbol,
                                                posOrderPrice.multiply(filledQuantityAmount));
                                BigDecimal lockedAmount =
                                        calculateAssetAmount(
                                                symbol, posOrderPrice.multiply(quantityAmount));
                                BigDecimal fee = BigDecimal.ZERO;
                                BigDecimal assetTotal = BigDecimal.ZERO;
                                BigDecimal income = BigDecimal.ZERO;
                                long experiencePoints = 0L;

                                // temp PosTrade for cal
                                PosTrade tempPosTrade = new PosTrade();
                                tempPosTrade.setProperties(
                                        symbol.getId(),
                                        posOrder.getUserId(),
                                        posOrder.getOrderSide(),
                                        posOrderPrice,
                                        filledQuantityAmount,
                                        TradeAction.TAKER,
                                        posOrder.getId());
                                tempPosTrade.setCalProperties(
                                        fee,
                                        posOrder.getOrderType(),
                                        posOrder.getOrderChannel(),
                                        orderbookService.getBestBidOfQuoteJpy(
                                                symbol.getCurrencyPair().getQuoteCurrency()),
                                        assetAmountScaled,
                                        UserIdType.Invest,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null);

                                Asset baseAsset =
                                        assetService.findOrCreate(
                                                posOrder.getUserId(),
                                                symbol.getCurrencyPair().getBaseCurrency());

                                if (COMPLETED.equals(result.status)
                                        || PART_CANCELED.equals(result.status)) {
                                    // get eval_profit_loss_amt, eval_profit_loss_amt_rate,
                                    // avg_acq_unit_price, now_price
                                    getCalValue calValues =
                                            getGetCalValue(
                                                    result,
                                                    symbol,
                                                    posOrder,
                                                    assetTotal,
                                                    tempPosTrade);
                                    // result amber
                                    if (posOrder.getOrderSide().isSell()) {

                                        assetService.updateWithExternalLock(
                                                posOrder.getUserId(),
                                                symbol.getCurrencyPair().getBaseCurrency(),
                                                filledQuantityAmount.negate(),
                                                quantityAmount.negate(),
                                                entityManager);

                                        // update jpy
                                        assetService.updateWithExternalLock(
                                                posOrder.getUserId(),
                                                symbol.getCurrencyPair().getQuoteCurrency(),
                                                assetAmountScaled,
                                                BigDecimal.ZERO,
                                                entityManager);
                                        // when sell, save Pos Order Trade History for cal monster
                                        income =
                                                posOrder.getAmount()
                                                        .multiply(
                                                                posOrder.getPrice()
                                                                        .subtract(
                                                                                baseAsset
                                                                                        .getAvgAcqUnitPrice()))
                                                        .setScale(4, RoundingMode.HALF_UP);

                                        // SET POWER By monster income
                                        experiencePoints =
                                                monsterExperienceService.calculateExperiencePoints(
                                                        tempPosTrade.getSymbolId(),
                                                        tempPosTrade.getIdType(),
                                                        income);
                                    } else {
                                        // update  Currency
                                        assetService.updateAssestAmountWithExternalLock(
                                                posOrder.getUserId(),
                                                symbol.getCurrencyPair().getBaseCurrency(),
                                                filledQuantityAmount,
                                                BigDecimal.ZERO,
                                                calValues.evalProfitLossAmt(),
                                                calValues.evalProfitLossAmtRate(),
                                                calValues.avgAcqUnitPrice(),
                                                calValues.assetTotal(),
                                                entityManager);

                                        // update jpy
                                        assetService.updateWithExternalLock(
                                                posOrder.getUserId(),
                                                symbol.getCurrencyPair().getQuoteCurrency(),
                                                assetAmountScaled.negate(),
                                                lockedAmount.negate(),
                                                entityManager);
                                    }
                                    createPosCoverOrder(
                                            result,
                                            posOrder,
                                            posOrderPrice,
                                            remainingAmount,
                                            getPosOrderStatus(result.status),
                                            entityManager);

                                    PosTrade posTrade =
                                            saveEntity(
                                                    symbol,
                                                    posOrder,
                                                    posOrderPrice,
                                                    filledQuantityAmount,
                                                    fee,
                                                    assetAmountScaled,
                                                    posOrder.getOrderSide().isSell()
                                                            ? calValues.evalProfitLossAmt()
                                                            : null,
                                                    posOrder.getOrderSide().isSell()
                                                            ? calValues.evalProfitLossAmtRate()
                                                            : null,
                                                    posOrder.getOrderSide().isSell()
                                                            ? baseAsset.getAvgAcqUnitPrice()
                                                            : null,
                                                    posOrder.getOrderSide().isSell()
                                                            ? income
                                                            : null,
                                                    posOrder.getOrderSide().isSell()
                                                            ? experiencePoints
                                                            : null,
                                                    posOrder.getNotes(),
                                                    entityManager);
                                    // SET POWER WEHEN BUY_SELL_TRADE
                                    choicePowerSyncService.savePowerByRule(
                                            posOrder.getUserId(),
                                            ChoiceActivityRuleEnum.BUY_SELL_TRADE,
                                            posTrade.getAssetAmount() == null
                                                    ? BigDecimal.ZERO
                                                    : posTrade.getAssetAmount());
                                    if (posTrade.getOrderSide().isSell()) {
                                        userMonsterInfoService.calculateMonster(income, posTrade);
                                    }
                                    posOrderService.sendTradeMail(
                                            posOrder,
                                            posTrade,
                                            mailNoreplyService,
                                            sesManager,
                                            symbol,
                                            user);
                                } else {
                                    if (posOrder.getOrderSide().isSell()) {
                                        assetService.updateWithExternalLock(
                                                posOrder.getUserId(),
                                                symbol.getCurrencyPair().getBaseCurrency(),
                                                BigDecimal.ZERO,
                                                quantityAmount.negate(),
                                                entityManager);
                                    } else {
                                        // update jpy
                                        assetService.updateWithExternalLock(
                                                posOrder.getUserId(),
                                                symbol.getCurrencyPair().getQuoteCurrency(),
                                                BigDecimal.ZERO,
                                                lockedAmount.negate(),
                                                entityManager);
                                    }
                                    createPosCoverOrder(
                                            result,
                                            posOrder,
                                            posOrderPrice,
                                            quantityAmount,
                                            getPosOrderStatus(result.status),
                                            entityManager);
                                    sendCustomerCancelMail(
                                            posOrder, mailNoreplyService, sesManager, symbol, user);
                                }
                            });
                })) {
            log.error(
                    getClass().getSimpleName()
                            + ",could not get lock. key: "
                            + getLockKey(
                                    posOrder.getUserId(),
                                    symbol.getCurrencyPair().getQuoteCurrency()));
            throw new CustomException(
                    ErrorCode.LOCK_KEY, "AssetUpdate userId: " + posOrder.getUserId());
        }
        ;
    }

    // MonsterGrowthLevelEntryを獲得する
    private static MonsterGrowthLevel getMonsterGrowthLevelEntry(
            Long experience, List<MonsterGrowthLevel> dataList) {
        // MonsterGrowthLevel
        MonsterGrowthLevel newLevelTbl = null;
        for (MonsterGrowthLevel levelTbl : dataList) {
            if (experience >= levelTbl.getRequiredExperience()) {
                newLevelTbl = levelTbl;
            } else {
                break;
            }
        }
        return newLevelTbl;
    }

    /**
     * get 評価損益額&評価損益額率&平均取得単価
     *
     * @param result
     * @param symbol
     * @param posOrder
     * @param assetTotal
     * @return
     */
    private getCalValue getGetCalValue(
            Result result,
            Symbol symbol,
            PosOrder posOrder,
            BigDecimal assetTotal,
            PosTrade tempPosTrade) {
        BigDecimal evalProfitLossAmtRate;
        BigDecimal avgAcqUnitPrice;
        BigDecimal nowPrice;
        BigDecimal totalAmount;
        BigDecimal evalProfitLossAmt;
        // get history assetamount
        List<PosTrade> posTradeAmountHistoryList =
                posTradeService.getPosTradeHistoryTotals(
                        symbol.getId(),
                        posOrder.getUserId(),
                        posOrder.getOrderSide(),
                        posOrder.getIdType());
        // get posTrade assetAmountTotal history
        BigDecimal historyAssetAmountTotal =
                posTradeAmountHistoryList.stream()
                        .map(
                                trade -> {
                                    BigDecimal tradeValue =
                                            trade.getPrice().multiply(trade.getAmount());
                                    return trade.getOrderSide() == OrderSide.BUY
                                            ? tradeValue
                                            : tradeValue.negate();
                                })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("get posTrade assetAmountTotal:{}", historyAssetAmountTotal);

        // get posTrade amountTotal history
        BigDecimal historyAmountTotal =
                posTradeAmountHistoryList.stream()
                        .map(
                                trade -> {
                                    return trade.getOrderSide() == OrderSide.BUY
                                            ? trade.getAmount()
                                            : trade.getAmount().negate();
                                })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("get posTrade amountTotal:{}", historyAmountTotal);

        if (historyAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
            historyAssetAmountTotal = BigDecimal.ZERO;
        }
        log.info("history asset amount total: {}", historyAssetAmountTotal);
        log.info("history amount total: {}", historyAmountTotal);

        // get posTrade
        List<PosTrade> posTradeAmountList =
                posTradeService.getPosTradeAmountTotal(
                        symbol.getId(),
                        posOrder.getUserId(),
                        posOrder.getOrderSide(),
                        posOrder.getIdType());

        // get posTrade assetAmountTotal
        BigDecimal posTradeAssetAmountTotal =
                posTradeAmountList.stream()
                        .map(
                                trade -> {
                                    BigDecimal tradeValue =
                                            trade.getPrice().multiply(trade.getAmount());
                                    return trade.getOrderSide() == OrderSide.BUY
                                            ? tradeValue
                                            : tradeValue.negate();
                                })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("get posTrade assetAmountTotal:{}", posTradeAssetAmountTotal);

        // get posTrade amountTotal
        BigDecimal posTradeAmountTotal =
                posTradeAmountList.stream()
                        .map(
                                trade -> {
                                    return trade.getOrderSide() == OrderSide.BUY
                                            ? trade.getAmount()
                                            : trade.getAmount().negate();
                                })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("get posTrade amountTotal:{}", posTradeAmountTotal);

        if (posTradeAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
            posTradeAssetAmountTotal = BigDecimal.ZERO;
        }

        totalAmount = historyAmountTotal.add(posTradeAmountTotal);

        // get temp postrade
        BigDecimal tempPosTradeAmount = tempPosTrade.getAmount();
        if (tempPosTradeAmount == null) {
            tempPosTradeAmount = BigDecimal.ZERO;
        }
        totalAmount = totalAmount.add(tempPosTradeAmount);
        BigDecimal tempPosTradePrice = tempPosTrade.getPrice();
        if (tempPosTradePrice == null) {
            tempPosTradePrice = BigDecimal.ZERO;
        }
        BigDecimal tempPosTradeAssetAmount = tempPosTradePrice.multiply(tempPosTradeAmount);
        if (tempPosTradeAssetAmount == null) {
            tempPosTradeAssetAmount = BigDecimal.ZERO;
        }
        assetTotal = historyAssetAmountTotal.add(posTradeAssetAmountTotal);
        assetTotal = assetTotal.add(tempPosTradeAssetAmount);

        if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            avgAcqUnitPrice = BigDecimal.ZERO;
        } else {
            // cal：(Z * Z1) + (Y * Y1) / (Z1 + Y1)
            avgAcqUnitPrice = assetTotal.divide(totalAmount, SCALE_3, BigDecimal.ROUND_HALF_UP);
        }
        log.info("get posTrade averagePrice:{}", avgAcqUnitPrice);

        nowPrice = new BigDecimal(result.fiatReference == null ? "0" : result.fiatReference.jpy);
        log.info("get now price:{}", nowPrice);

        evalProfitLossAmt =
                totalAmount
                        .multiply(nowPrice.subtract(avgAcqUnitPrice))
                        .setScale(
                                symbol.getCurrencyPair().getPosBasePrecision(),
                                BigDecimal.ROUND_HALF_UP);
        log.info("get evalProfitLossAmt:{}", evalProfitLossAmt);

        evalProfitLossAmtRate =
                (assetTotal.compareTo(BigDecimal.ZERO) == 0)
                        ? BigDecimal.ZERO
                        : evalProfitLossAmt.divide(
                                assetTotal,
                                symbol.getCurrencyPair().getPosBasePrecision(),
                                BigDecimal.ROUND_HALF_UP);
        log.info("get evalProfitLossAmtRate:{}", evalProfitLossAmtRate);
        // first buy
        if (avgAcqUnitPrice.compareTo(BigDecimal.ZERO) == 0) {
            avgAcqUnitPrice = posOrder.getPrice();
        }
        return new getCalValue(
                avgAcqUnitPrice, evalProfitLossAmt, evalProfitLossAmtRate, nowPrice, assetTotal);
    }

    private record getCalValue(
            BigDecimal avgAcqUnitPrice,
            BigDecimal evalProfitLossAmt,
            BigDecimal evalProfitLossAmtRate,
            BigDecimal nowPrice,
            BigDecimal assetTotal) {}

    private void createPosCoverOrder(
            Result result,
            PosOrder posOrder,
            BigDecimal priceAmount,
            BigDecimal remainingAmount,
            PosOrderStatus posOrderStatus,
            EntityManager entityManager)
            throws Exception {
        posOrder.setRemainingAmount(remainingAmount);
        posOrder.setOrderStatus(posOrderStatus);
        posOrder.setCovered(Boolean.TRUE);
        posOrderService.save(posOrder, entityManager);

        PosCoverOrder posCoverOrder = new PosCoverOrder();
        posCoverOrder.setSymbolId(posOrder.getSymbolId());
        posCoverOrder.setOrderId(posOrder.getId());
        posCoverOrder.setOrderSide(OrderSide.valueOf(result.direction));
        posCoverOrder.setOrderType(OrderType.valueOf(result.type));
        posCoverOrder.setPrice(priceAmount);
        posCoverOrder.setAveragePrice(
                new BigDecimal(result.fiatReference == null ? "0" : result.fiatReference.jpy));
        posCoverOrder.setAmount(posOrder.getAmount());
        posCoverOrder.setRemainingAmount(remainingAmount);
        posCoverOrder.setFee(new BigDecimal(result.fee));
        posCoverOrder.setStrategy(STRATEGY);
        posCoverOrder.setExchange(Exchange.AMBER);
        posCoverOrder.setMmOrderId(result.id);
        posCoverOrder.setOrderStatus(getOrderStatus(result.status));
        posCoverOrder.setUsdtPrice(new BigDecimal(result.price == null ? "0" : result.price));
        this.save(posCoverOrder, entityManager);
    }

    private Result getRequestTradeOrdersPos(
            String payload, PosOrder posOrder, Symbol symbol, User user) throws Exception {
        long timestamp = System.currentTimeMillis();
        String signStr =
                "method="
                        + GET_PRICE_METHOD
                        + "&path="
                        + apiPath
                        + "&timestamp="
                        + timestamp
                        + "&body="
                        + payload;
        log.info("signStr:{}", signStr);
        String sign = new HmacUtils(SIGN_ALGORITHM, accessSecret).hmacHex(signStr);

        ArrayList<String> headers = new ArrayList<>(6);
        headers.add("access-key");
        headers.add(accessKey);
        headers.add("access-timestamp");
        headers.add(String.valueOf(timestamp));
        headers.add("access-sign");
        headers.add(sign);

        String requestUrl = apiHost + apiPath;
        HttpClient.HttpResult result = HttpClient.httpPost(requestUrl, headers, payload);
        log.info("request API result:{}", JsonUtil.encode(result));
        if (HttpURLConnection.HTTP_INTERNAL_ERROR == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Internal Server Error",
                    requestUrl,
                    result.code,
                    result.content);
            return null;
        }
        if (HttpURLConnection.HTTP_BAD_REQUEST == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Bad request/Request failed",
                    requestUrl,
                    result.code,
                    result.content);
            assetUpdate(symbol, posOrder);
            sendCustomerCancelMail(posOrder, mailNoreplyService, sesManager, symbol, user);
            return null;
        }
        if (HttpURLConnection.HTTP_UNAUTHORIZED == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Unauthorize",
                    requestUrl,
                    result.code,
                    result.content);
            assetUpdate(symbol, posOrder);
            sendCustomerCancelMail(posOrder, mailNoreplyService, sesManager, symbol, user);
            return null;
        }
        if (HttpURLConnection.HTTP_FORBIDDEN == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Forbidden/IP restriction",
                    requestUrl,
                    result.code,
                    result.content);
            assetUpdate(symbol, posOrder);
            sendCustomerCancelMail(posOrder, mailNoreplyService, sesManager, symbol, user);
            return null;
        }
        if (HttpURLConnection.HTTP_NOT_FOUND == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Not found",
                    requestUrl,
                    result.code,
                    result.content);
            assetUpdate(symbol, posOrder);
            sendCustomerCancelMail(posOrder, mailNoreplyService, sesManager, symbol, user);
            return null;
        }
        if (HttpURLConnection.HTTP_UNSUPPORTED_TYPE == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Unsupported media type",
                    requestUrl,
                    result.code,
                    result.content);
            assetUpdate(symbol, posOrder);
            sendCustomerCancelMail(posOrder, mailNoreplyService, sesManager, symbol, user);
            return null;
        }
        if (TOO_MANY_REQUESTS == result.code) {
            log.warn(
                    "Failed to request API {}. code: {}, msg: {},Too many requests",
                    requestUrl,
                    result.code,
                    result.content);
            assetUpdate(symbol, posOrder);
            sendCustomerCancelMail(posOrder, mailNoreplyService, sesManager, symbol, user);
            return null;
        }
        String responseJson = result.content;
        ResponseOrder responseObj = JsonUtil.decode(responseJson, ResponseOrder.class);
        if (ObjectUtils.isEmpty(responseObj)) {
            log.warn("Failed to parse json for get best price of the amber: {}", responseJson);
            assetUpdate(symbol, posOrder);
            return null;
        }
        Result resultObj = responseObj.result;
        if (Objects.isNull(resultObj)) {
            log.warn("No price returned from amber: {}", responseJson);
            assetUpdate(symbol, posOrder);
            return null;
        }

        if (responseObj.code == 0) {
            if (Objects.isNull(resultObj)) {
                log.warn("No price returned from amber: {}", responseJson);
                assetUpdate(symbol, posOrder);
                return null;
            }
            return resultObj;
        } else {
            log.warn("The result object does not expected: {}", responseObj.msg);
            assetUpdate(symbol, posOrder);
            return null;
        }
    }

    public record RequestOrder(
            String direction,
            String price,
            String strategy,
            String type,
            String clientId,
            String symbol,
            String quantity) {}

    public record FiatReference(String jpy) {}

    public record ResponseOrder(Result result, int code, String msg) {}

    public record Result(
            String type,
            String direction,
            String status,
            String strategy,
            String quantity,
            String filledQuantity,
            String id,
            String symbol,
            String price,
            FiatReference fiatReference,
            String baseQuantity,
            String quoteQuantity,
            String filledPrice,
            String filledBaseQuantity,
            String filledQuoteQuantity,
            String fee,
            String feeCcy,
            String isQuote,
            String createdAt,
            String updatedAt) {}

    private BigDecimal calculateAssetAmount(Symbol symbol, BigDecimal amountWithoutScale) {

        // 約定総額算出
        // 約定価格×約定数量(部分約定含む)
        // 資産計算は桁数処理を行う（切り上げ）

        int assetPrecision = symbol.getCurrencyPair().getAssetPrecision();

        // 指定桁数+1桁で切り上げ
        // 指定桁数2桁のとき、4桁(指定桁数+2)で切り捨てしてから、3桁(指定桁数+1)で切り上げ
        // ケース1(未満) fee少数部 < min :0.0009 => 0.000 => 0
        // ケース2(同値) fee少数部 = min :0.001 => 0.01
        // ケース3(超過) fee少数部 > min :0.002 => 0.01

        // ケース1(未満) fee少数部 < min :0.1009 => 0.1
        // ケース2(同値) fee少数部 = min :0.101 => 0.11
        // ケース3(超過) fee少数部 > min :0.102 => 0.11

        // ケース1(未満) fee少数部 < min :123.0009 => 123
        // ケース2(同値) fee少数部 = min :123.001 => 123.01
        // ケース3(超過) fee少数部 > min :123.002 => 123.01

        // 指定桁数+2桁で切り捨てしてから 指定桁数+1桁で切り上げ
        BigDecimal assetAmountWithoutScalePlus =
                amountWithoutScale.setScale(assetPrecision + 1, RoundingMode.DOWN);

        return symbol.getCurrencyPair()
                .getScaledAsset(assetAmountWithoutScalePlus, RoundingMode.UP);
    }

    public PosTrade saveEntity(
            Symbol symbol,
            PosOrder posOrder,
            BigDecimal posOrderPrice,
            BigDecimal remainingAmount,
            BigDecimal fee,
            BigDecimal assetAmountScaled,
            BigDecimal evalProfitLossAmt,
            BigDecimal evalProfitLossAmtRate,
            BigDecimal avgAcqUnitPrice,
            BigDecimal income,
            Long experiencePoints,
            String notes,
            EntityManager entityManager)
            throws Exception {
        PosTrade posTrade = posTradeService.newEntity();
        posTrade.setProperties(
                symbol.getId(),
                posOrder.getUserId(),
                posOrder.getOrderSide(),
                posOrderPrice,
                remainingAmount,
                TradeAction.TAKER,
                posOrder.getId());
        posTrade.setCalProperties(
                fee,
                posOrder.getOrderType(),
                posOrder.getOrderChannel(),
                orderbookService.getBestBidOfQuoteJpy(symbol.getCurrencyPair().getQuoteCurrency()),
                assetAmountScaled,
                UserIdType.Invest,
                evalProfitLossAmt,
                evalProfitLossAmtRate,
                avgAcqUnitPrice,
                income,
                experiencePoints);
        posTrade.setNotes(notes);

        // -- START
        // https://backseat-inc.atlassian.net/browse/OFLM-1496
        if (OrderSide.SELL.equals(posOrder.getOrderSide())) {
            Long userId = posOrder.getUserId();
            Asset asset = assetService.findOne(userId, symbol.getCurrencyPair().getBaseCurrency());
            Integer userCropStage =
                    userCropStatusService
                            .findBy(userId, asset.getId(), symbol.getTradeType())
                            .map(UserCropStatus::getGrowthStageId)
                            .orElse(NumberUtils.INTEGER_ZERO);
            posTrade.setUserGrowthStageId(userCropStage);
        } else {
            posTrade.setUserGrowthStageId(NumberUtils.INTEGER_ZERO);
        }
        // https://backseat-inc.atlassian.net/browse/OFLM-1496
        // --END
        return posTradeService.save(posTrade, entityManager);
    }

    private void assetUpdate(Symbol symbol, PosOrder posOrder) throws Exception {

        if (!redisManager.executeWithLock(
                getLockKey(posOrder.getUserId(), symbol.getCurrencyPair().getQuoteCurrency()),
                LockParams.EXECUTE_ORDER,
                () -> {
                    customTransactionManager.execute(
                            entityManager -> {
                                BigDecimal quantityAmount = posOrder.getRemainingAmount();
                                BigDecimal posOrderPrice = posOrder.getPrice();
                                BigDecimal lockedAmount =
                                        calculateAssetAmount(
                                                symbol, posOrderPrice.multiply(quantityAmount));
                                if (posOrder.getOrderSide().isSell()) {
                                    assetService.updateWithExternalLock(
                                            posOrder.getUserId(),
                                            symbol.getCurrencyPair().getBaseCurrency(),
                                            BigDecimal.ZERO,
                                            quantityAmount.negate(),
                                            entityManager);
                                } else {
                                    // update jpy
                                    assetService.updateWithExternalLock(
                                            posOrder.getUserId(),
                                            symbol.getCurrencyPair().getQuoteCurrency(),
                                            BigDecimal.ZERO,
                                            lockedAmount.negate(),
                                            entityManager);
                                }
                                Result result =
                                        new Result(
                                                posOrder.getOrderType().toString(),
                                                posOrder.getOrderSide().getName(),
                                                getOrderStatus("CANCELED").toString(),
                                                null,
                                                null,
                                                null,
                                                posOrder.getId().toString(),
                                                null,
                                                null,
                                                new FiatReference(BigDecimal.ZERO.toString()),
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                BigDecimal.ZERO.toString(),
                                                null,
                                                null,
                                                null,
                                                null);
                                createPosCoverOrder(
                                        result,
                                        posOrder,
                                        posOrderPrice,
                                        quantityAmount,
                                        PosOrderStatus.FAILED_FILLED,
                                        entityManager);
                            });
                })) {
            log.error(
                    getClass().getSimpleName()
                            + ",could not get lock. key: "
                            + getLockKey(
                                    posOrder.getUserId(),
                                    symbol.getCurrencyPair().getQuoteCurrency()));
            throw new CustomException(
                    ErrorCode.LOCK_KEY, "AssetUpdate userId: " + posOrder.getUserId());
        }
        ;
    }

    private OrderStatus getOrderStatus(String orderStatus) {
        switch (orderStatus) {
            case "COMPLETED":
                return OrderStatus.FULLY_FILLED;
            case "PART_CANCELED":
                return OrderStatus.PARTIALLY_FILLED;
            default:
                return OrderStatus.CANCELED_UNFILLED;
        }
    }

    private PosOrderStatus getPosOrderStatus(String orderStatus) {
        switch (orderStatus) {
            case "COMPLETED":
                return PosOrderStatus.FULLY_FILLED;
            case "PART_CANCELED":
                return PosOrderStatus.PARTIALLY_FILLED;
            default:
                return PosOrderStatus.FAILED_FILLED;
        }
    }

    private void sendCustomerCancelMail(
            PosOrder posOrder,
            MailNoreplyService mailNoreplyService,
            SesManager sesManager,
            Symbol symbol,
            User user) {
        try {
            if (user == null) {
                user = userService.findOne(posOrder.getUserId());
            }
            if (user.isTradeUncapped()) {
                return;
            }
            // get mail template
            var mailInfo = mailNoreplyService.findOne(MailNoreplyType.POS_FAILED_ORDER);
            var mailTemplate = mailInfo.getContents();
            mailTemplate =
                    mailTemplate.replace("${instrumentId}", symbol.getCurrencyPair().getName());
            mailTemplate = mailTemplate.replace("${side}", posOrder.getOrderSide().toString());
            mailTemplate = mailTemplate.replace("${orderType}", posOrder.getOrderType().toString());
            mailTemplate =
                    mailTemplate.replace(
                            "${size}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledAmount(posOrder.getAmount())
                                            .toPlainString()));
            mailTemplate =
                    mailTemplate.replace(
                            "${price}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledPrice(posOrder.getPrice())
                                            .toPlainString()));
            mailTemplate = mailTemplate.replace("${orderId}", posOrder.getId().toString());
            mailTemplate =
                    mailTemplate.replace(
                            "${remaining_amount}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledAmount(posOrder.getRemainingAmount())
                                            .toPlainString()));
            // send mail
            sesManager.send(
                    mailInfo.getFromAddress(), user.getEmail(), mailInfo.getTitle(), mailTemplate);
        } catch (Exception e) {
            log.error("sendCustomerCancelMail error", e);
        }
    }
}
