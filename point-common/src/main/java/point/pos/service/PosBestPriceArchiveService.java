package point.pos.service;

import java.math.BigDecimal;
import java.sql.Types;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.ArgumentTypePreparedStatementSetter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import point.common.component.HistoricalTransactionManager;
import point.common.entity.Symbol;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = "historicalJpaTransactionManager")
@RequiredArgsConstructor
public class PosBestPriceArchiveService {
    private static final String ARCHIVE_SQL =
            "insert into pos_best_price (symbol_id, best_ask, best_bid) values (?, ?, ?)";

    private final HistoricalTransactionManager historicalTransactionManager;

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = "historicalJpaTransactionManager")
    public void archive(Symbol symbol, BigDecimal bestAsk, BigDecimal bestBid) {

        if (Objects.isNull(bestAsk) || Objects.isNull(bestBid)) {
            return;
        }

        ArgumentTypePreparedStatementSetter archiveParameter =
                new ArgumentTypePreparedStatementSetter(
                        new Object[] {symbol.getId(), bestAsk, bestBid},
                        new int[] {Types.BIGINT, Types.NUMERIC, Types.NUMERIC});
        historicalTransactionManager.archiveWithParameter(ARCHIVE_SQL, archiveParameter);
        log.info(
                "archive_log_pos_best_price,symbolId,{},symbolName,{}",
                symbol.getId(),
                symbol.getName());
    }
}
