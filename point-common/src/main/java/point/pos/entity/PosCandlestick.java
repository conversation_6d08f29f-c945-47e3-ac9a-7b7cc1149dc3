package point.pos.entity;

import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.NoArgsConstructor;
import lombok.ToString;
import point.common.entity.Candlestick;

@Entity
@NoArgsConstructor
@Table(name = "pos_candlestick")
@ToString(callSuper = true, doNotUseGetters = true)
public class PosCandlestick extends Candlestick {

    private static final long serialVersionUID = -5504292654430309352L;
}
