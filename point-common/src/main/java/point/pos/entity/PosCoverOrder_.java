package point.pos.entity;

import java.math.BigDecimal;
import java.util.List;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Exchange;
import point.common.constant.OrderSide;
import point.common.constant.OrderStatus;
import point.common.constant.OrderType;
import point.common.entity.AbstractEntity_;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PosCoverOrder.class)
public abstract class PosCoverOrder_ extends AbstractEntity_ {

    public static volatile SingularAttribute<PosCoverOrder, Long> symbolId;
    public static volatile SingularAttribute<PosCoverOrder, Long> orderId;
    public static volatile SingularAttribute<PosCoverOrder, OrderSide> orderSide;
    public static volatile SingularAttribute<PosCoverOrder, OrderType> orderType;
    public static volatile SingularAttribute<PosCoverOrder, BigDecimal> price;
    public static volatile SingularAttribute<PosCoverOrder, BigDecimal> averagePrice;
    public static volatile SingularAttribute<PosCoverOrder, BigDecimal> amount;
    public static volatile SingularAttribute<PosCoverOrder, BigDecimal> remainingAmount;
    public static volatile SingularAttribute<PosCoverOrder, BigDecimal> remainingAmountManualNidt;
    public static volatile SingularAttribute<PosCoverOrder, BigDecimal> fee;
    public static volatile SingularAttribute<PosCoverOrder, Exchange> exchange;
    public static volatile SingularAttribute<PosCoverOrder, Long> mmOrderId;
    public static volatile SingularAttribute<PosCoverOrder, List<OrderStatus>> orderStatus;
    public static volatile SingularAttribute<PosCoverOrder, PosOrder> posOrder;
    public static volatile SingularAttribute<PosCoverOrder, BigDecimal> usdtPrice;
    public static volatile SingularAttribute<PosCoverOrder, BigDecimal> averagePriceManual;
    public static volatile SingularAttribute<PosCoverOrder, String> strategy;

    public static final String SYMBOL_ID = "symbolId";
    public static final String ORDER_ID = "orderId";
    public static final String ORDER_SIDE = "orderSide";
    public static final String ORDER_TYPE = "orderType";
    public static final String PRICE = "price";
    public static final String AVERAGE_PRICE = "averagePrice";
    public static final String AMOUNT = "amount";
    public static final String REMAINING_AMOUNT = "remainingAmount";
    public static final String FEE = "fee";
    public static final String EXCHANGE = "exchange";
    public static final String ORDER_STATUS = "orderStatus";
    public static final String AVERAGE_PRICE_MANUAL = "averagePriceManual";
    public static final String POS_ORDER = "posOrder";
    public static final String USDT_PRICE = "usdtPrice";
    public static final String STRATEGY = "strategy";
}
