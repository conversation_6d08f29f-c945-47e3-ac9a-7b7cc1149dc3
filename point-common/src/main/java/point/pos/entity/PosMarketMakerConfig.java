package point.pos.entity;

import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Exchange;
import point.common.entity.AbstractEntity;

@Entity
@NoArgsConstructor
@Setter
@Getter
@Table(name = "pos_market_maker_config")
public class PosMarketMakerConfig extends AbstractEntity {
    @Column(name = "symbol_id")
    private Long symbolId;

    @Column(name = "mm")
    @Enumerated(EnumType.STRING)
    private Exchange exchange;

    private Boolean enabled;

    private BigDecimal quantity;
}
