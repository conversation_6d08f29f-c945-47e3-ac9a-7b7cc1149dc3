cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
# refered by logback-spring.xml
common:
  log:
    console:
      appender: CONSOLE_JSON
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: worker.stg.cxr-inc.com
    environment: stg
  datasource:
    master:
      maximum-pool-size: 150
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false

point-pos:
  best-price:
    amber:
      api-host: https://be-alpha.whalefin.com
  base-trade:
    amber:
      api-host: https://be-alpha.whalefin.com

logging:
  level:
    root: info
    com:
      zaxxer:
        hikari:
          HikariConfig: DEBUG
          pool:
            HikariPool: DEBUG