package point.mmh.component;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.StringJoiner;
import net.logstash.logback.argument.StructuredArguments;
import net.logstash.logback.marker.Markers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ClassUtils;
import point.common.component.RedisManager;
import point.common.component.RedisManager.LockParams;
import point.common.entity.Symbol;
import point.common.exception.CustomException;
import point.common.model.request.WorkerForm;
import point.common.service.SymbolService;

public abstract class Worker {

    private static final Logger log = LoggerFactory.getLogger(Worker.class);

    @Autowired private RedisManager redisManager;

    @Autowired private SymbolService symbolService;

    public abstract void execute(Symbol symbol, Map<String, Object> params) throws Exception;

    public final void work(WorkerForm workerForm) {
        Symbol symbol = getSymbolFromWorkerForm(workerForm);
        String workerName = createWorkerName(workerForm, symbol);

        String beanName = ClassUtils.getShortNameAsProperty(getClass());
        String workerSession = workerName + "-" + System.currentTimeMillis();

        MDC.put("worker", beanName == null ? "workerName:" + workerName : beanName);
        MDC.put("workerSession", workerSession);

        try {
            workMain(symbol, workerName, workerForm);
        } catch (Exception e) {
            // docker のログ出力における１行の最大文字数は 16k 固定らしいので、
            // 長すぎるメッセージを一部省略する。
            String message = e.getMessage();

            if (message.length() > 16000) {
                String shortMessage = message.substring(0, 1000) + "...";

                e =
                        new Exception(
                                "class="
                                        + e.getClass().getName()
                                        + ", shortened message="
                                        + shortMessage,
                                e.getCause());
            }

            log.error(Markers.append("workerForm", workerForm), "error on worker main process", e);
        } finally {
            MDC.remove("workerBean");
            MDC.remove("workerSession");
        }
    }

    protected void workMain(Symbol symbol, String workerName, WorkerForm workerForm)
            throws CustomException, Exception {
        String key = RedisManager.getLockKey(workerName);

        log.debug("try lock: key=" + key);

        if (!redisManager.lock(key, LockParams.WORKER)) {
            log.info("lock failed: key=" + key);
            return;
        }

        OffsetDateTime start = OffsetDateTime.now();

        log.info("start");

        try {
            execute(symbol, workerForm.getParams());
        } finally {
            redisManager.unlock(key);

            OffsetDateTime end = OffsetDateTime.now();
            String startString = start.format(DateTimeFormatter.ISO_DATE_TIME);
            String endString = end.format(DateTimeFormatter.ISO_DATE_TIME);

            log.info(
                    "end",
                    StructuredArguments.value("symbolId", symbol != null ? symbol.getId() : ""),
                    StructuredArguments.value("start", startString),
                    StructuredArguments.value("end", endString),
                    StructuredArguments.value(
                            "durationMillis", Duration.between(start, end).toMillis()));
        }
    }

    private String createWorkerName(WorkerForm workerForm, Symbol symbol) {
        StringJoiner stringJoiner = new StringJoiner("-");
        stringJoiner.add(workerForm.getMethod());

        if (symbol != null) {
            stringJoiner.add(symbol.getId().toString());
        }

        String workerName = stringJoiner.toString();
        return workerName;
    }

    private Symbol getSymbolFromWorkerForm(WorkerForm workerForm) {
        Long symbolId =
                workerForm.getParams().get("symbolId") != null
                        ? ((Number) workerForm.getParams().get("symbolId")).longValue()
                        : null;
        Symbol symbol = null;

        if (symbolId != null) {
            symbol = symbolService.findOne(symbolId);
        }

        return symbol;
    }
}
