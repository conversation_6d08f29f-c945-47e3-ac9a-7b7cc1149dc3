package point.mmh.component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;
import point.common.component.SnsManager;
import point.common.config.SpringConfig;
import point.common.constant.ErrorCode;
import point.common.constant.LogGroup;
import point.common.entity.Symbol;
import point.common.entity.WorkerMaster;
import point.common.exception.SqsCallbackException;
import point.common.model.request.WorkerForm;
import point.common.model.sqs.MessageBodyData;
import point.common.service.SymbolService;
import point.common.service.WorkerMasterService;
import point.common.util.DateUnit;
import point.common.util.JsonUtil;

@Slf4j
@RequiredArgsConstructor
@Component
public class SqsCallback {

    @RequiredArgsConstructor
    private static class WorkerMasterRunner implements Runnable {

        private final Long workerMasterId;

        private final SnsManager snsManager;

        private final String queueName;

        private final WorkerForm workerForm;

        private final WorkerMasterService workerMasterService;

        @Override
        public void run() {
            String wokerFormJson = JsonUtil.encode(workerForm);

            log.debug("start: workerForm=" + wokerFormJson);

            long startTime = System.currentTimeMillis();
            ScheduledExecutorService scheduledExecutorService =
                    Executors.newSingleThreadScheduledExecutor();

            scheduledExecutorService.scheduleAtFixedRate(
                    () -> {
                        long elaspedTime = System.currentTimeMillis() - startTime;

                        try {
                            log.debug(
                                    "start scheduled task: elaspedTime="
                                            + elaspedTime
                                            + " millis, workerForm="
                                            + wokerFormJson);

                            if (elaspedTime >= DateUnit.MINUTE.getMillis()) {
                                scheduledExecutorService.shutdown();
                                return;
                            }

                            if (WorkerDaemon.isShutdown()) {
                                log.debug("catch shutdown event");
                                scheduledExecutorService.shutdown();
                                return;
                            }

                            WorkerMaster workerMaster = workerMasterService.findOne(workerMasterId);

                            if (workerMaster == null || !workerMaster.isEnabled()) {
                                log.debug("detect workerMaster is disabled");
                                scheduledExecutorService.shutdown();
                                return;
                            }

                            snsManager.publish(queueName, workerForm);

                            log.debug("end scheduled task");
                        } catch (Exception e) {
                            log.error(LogGroup.ALERT.getName(), e);
                            scheduledExecutorService.shutdown();
                        }
                    },
                    0,
                    workerMasterService.findOne(workerMasterId).getIntervalMillis(),
                    TimeUnit.MILLISECONDS);
        }
    }

    @RequiredArgsConstructor
    private static class WorkerRunner implements Runnable {

        private final Worker worker;
        private final WorkerForm workerForm;

        @Override
        public void run() {
            worker.work(workerForm);
        }
    }

    private final ApplicationContext applicationContext;

    private final AsyncTaskExecutor taskExecutor;

    private final SnsManager snsManager;

    private final SpringConfig springConfig;

    private final SymbolService symbolService;

    private final WorkerMasterService workerMasterService;

    private final SqsBeanName sqsBeanName;

    public void callback(String queueName, String message) throws Exception {
        if (StringUtils.isEmpty(message)) {
            throw new SqsCallbackException(ErrorCode.SQS_ERROR_MESSAGE_BODY_DATA_NOT_FOUND);
        }

        log.debug("message=" + message);

        MessageBodyData messageBodyData = JsonUtil.decode(message, MessageBodyData.class);

        if (messageBodyData == null) {
            throw new SqsCallbackException(ErrorCode.SQS_ERROR_INVALID_MESSAGE_BODY_DATA);
        }

        String beanName = sqsBeanName.fromQueueName(queueName);

        log.debug("beanName=" + beanName + ", workerForm=" + messageBodyData.getMessage());

        WorkerForm workerForm = JsonUtil.decode(messageBodyData.getMessage(), WorkerForm.class);

        if (StringUtils.isEmpty(workerForm.getMethod())) {
            workerMasterService
                    .findByEnvironmentAndBeanName(springConfig.getEnvironment(), beanName)
                    .forEach(
                            workerMaster -> {
                                WorkerForm form =
                                        JsonUtil.decode(
                                                messageBodyData.getMessage(), WorkerForm.class);
                                form.setMethod(queueName);
                                form.getParams().put("workerMasterId", workerMaster.getId());

                                String formJson = JsonUtil.encode(form);

                                Symbol symbol =
                                        symbolService.findByCondition(
                                                workerMaster.getTradeType(),
                                                workerMaster.getCurrencyPair());

                                if (symbol == null) {
                                    if (workerMaster.getIntervalMillis() == null) {
                                        log.debug("publish: form=" + formJson);
                                        snsManager.publish(queueName, form);
                                    } else {
                                        log.debug("execute task: form=" + formJson);
                                        taskExecutor.execute(
                                                new WorkerMasterRunner(
                                                        workerMaster.getId(),
                                                        snsManager,
                                                        queueName,
                                                        form,
                                                        workerMasterService));
                                    }
                                } else {
                                    WorkerForm form2 =
                                            new WorkerForm(form.getMethod(), form.getParams());
                                    form2.getParams().put("symbolId", symbol.getId());

                                    if (workerMaster.getIntervalMillis() == null) {
                                        log.debug(
                                                "publish: symbol=" + symbol + ", form=" + formJson);
                                        snsManager.publish(queueName, form2);
                                    } else {
                                        log.debug(
                                                "execute task: symbol="
                                                        + symbol
                                                        + ", form="
                                                        + formJson);
                                        taskExecutor.execute(
                                                new WorkerMasterRunner(
                                                        workerMaster.getId(),
                                                        snsManager,
                                                        queueName,
                                                        form2,
                                                        workerMasterService));
                                    }
                                }
                            });
        } else {
            long workerMasterId =
                    ((Number) workerForm.getParams().get("workerMasterId")).longValue();
            log.debug(
                    "execute task: workerMasterId="
                            + workerMasterId
                            + ", workerForm="
                            + JsonUtil.encode(workerForm));
            WorkerMaster workerMaster = workerMasterService.findOne(workerMasterId);

            if (workerMaster.isEnabled()) {
                taskExecutor.execute(
                        new WorkerRunner(
                                (Worker) applicationContext.getBean(beanName), workerForm));
            }
        }
    }
}
